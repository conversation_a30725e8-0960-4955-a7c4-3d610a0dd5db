User registration failed due to duplicate email email=<EMAIL>
enterprise_error               category=business_rule correlation_id=unknown error_code=BUSINESS_RULE_EMAIL_ALREADY_EXISTS message=Business rule 'EMAIL_ALREADY_EXISTS' violated: A user with the email '<EMAIL>' already exists. operation=None recoverable=True severity=medium user_id=None
retry_exhausted                attempts=1 correlation_id=req_e6646c17ba83 error=Business rule 'EMAIL_ALREADY_EXISTS' violated: A user with the email '<EMAIL>' already exists.
{"method": "POST", "path": "/api/v1/register", "status_code": 400, "duration_ms": 437.7167224884033, "event": "http_request", "timestamp": "2025-06-27T20:44:08.464819Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 400, "duration_ms": 437.7167224884033, "event": "http_request", "timestamp": "2025-06-27T20:44:08.464819Z", "level": "info"}
_____________ TestRegistrationEndpoint.test_register_weak_password ______________ 
tests\test_routes_comprehensive.py:82: in test_register_weak_password
    assert response.status_code == 422  # Validation error
E   assert 400 == 422
E    +  where 400 = <Response [400 Bad Request]>.status_code
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=req_97ac4c314117
 email=<EMAIL> user_agent=python-httpx/0.28.1
enterprise_error               category=validation correlation_id=unknown error_code=VALIDATION_FAILED message=Password does not meet security requirements operation=None recoverable=True severity=low user_id=None
retry_exhausted                attempts=1 correlation_id=req_97ac4c314117 error=Password does not meet security requirements
{"method": "POST", "path": "/api/v1/register", "status_code": 400, "duration_ms": 10.920286178588867, "event": "http_request", "timestamp": "2025-06-27T20:44:08.567062Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 400, "duration_ms": 10.920286178588867, "event": "http_request", "timestamp": "2025-06-27T20:44:08.567062Z", "level": "info"}
_____________ TestRegistrationEndpoint.test_register_when_disabled ______________
tests\test_routes_comprehensive.py:99: in test_register_when_disabled
    assert response.status_code == 400
E   assert 201 == 400
E    +  where 201 = <Response [201 Created]>.status_code
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=req_d77bc906b007
 email=<EMAIL> user_agent=python-httpx/0.28.1
Attempting to register new user email=<EMAIL>
User registered successfully   email=<EMAIL> user_id=14     
{"user_id": "14", "method": "email_password", "event_type": "business_event", "event": "user_registered", "timestamp": "2025-06-27T20:44:08.961152Z", "level": "info"}
user_registration_success      correlation_id=req_d77bc906b007 email=<EMAIL> user_id=14
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 350.0096797943115, "event": "http_request", "timestamp": "2025-06-27T20:44:08.961152Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 350.0096797943115, "event": "http_request", "timestamp": "2025-06-27T20:44:08.961152Z", "level": "info"}
_______________ TestLoginEndpoint.test_login_invalid_credentials ________________ 
tests\test_routes_comprehensive.py:148: in test_login_invalid_credentials
    assert response.status_code == 401
E   assert 500 == 401
E    +  where 500 = <Response [500 Internal Server Error]>.status_code
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=req_3a105eaf59f8
 email=<EMAIL> user_agent=python-httpx/0.28.1
Attempting to register new user email=<EMAIL>
User registered successfully   email=<EMAIL> user_id=16     
{"user_id": "16", "method": "email_password", "event_type": "business_event", "event": "user_registered", "timestamp": "2025-06-27T20:44:09.893865Z", "level": "info"}
user_registration_success      correlation_id=req_3a105eaf59f8 email=<EMAIL> user_id=16
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 317.86489486694336, "event": "http_request", "timestamp": "2025-06-27T20:44:09.894873Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 317.86489486694336, "event": "http_request", "timestamp": "2025-06-27T20:44:09.894873Z", "level": "info"}
login_attempt                  client_ip=127.0.0.1 correlation_id=req_e570b21aa6b6
 email=<EMAIL>
Login attempt                  email=<EMAIL>
Login failed: Invalid credentials email=<EMAIL>
enterprise_error               category=security correlation_id=unknown error_code=SECURITY_INVALID_CREDENTIALS message=Security violation (INVALID_CREDENTIALS): Invalid email or password provided. operation=None recoverable=False severity=critical user_id=None
{"email": "<EMAIL>", "success": false, "method": "email_password", "event_type": "business_event", "event": "login_attempt", "timestamp": "2025-06-27T20:44:10.198001Z", "level": "info"}
login_failure                  correlation_id=req_e570b21aa6b6 email=<EMAIL> error_code=SECURITY_INVALID_CREDENTIALS
retry_exhausted                attempts=1 correlation_id=req_e570b21aa6b6 error=Security violation (INVALID_CREDENTIALS): Invalid email or password provided.       
{"method": "POST", "path": "/api/v1/login", "status_code": 500, "duration_ms": 301.23162269592285, "event": "http_request", "timestamp": "2025-06-27T20:44:10.198001Z", "level": "info"}
{"method": "POST", "path": "/api/v1/login", "status_code": 500, "duration_ms": 301.23162269592285, "event": "http_request", "timestamp": "2025-06-27T20:44:10.198001Z", "level": "info"}
_________________ TestLoginEndpoint.test_login_nonexistent_user _________________ 
tests\test_routes_comprehensive.py:160: in test_login_nonexistent_user
    assert response.status_code == 401
E   assert 500 == 401
E    +  where 500 = <Response [500 Internal Server Error]>.status_code
----------------------------- Captured stdout call ------------------------------
login_attempt                  client_ip=127.0.0.1 correlation_id=req_e8e881814c25
 email=<EMAIL>
Login attempt                  email=<EMAIL>
Login failed: Invalid credentials email=<EMAIL>
enterprise_error               category=security correlation_id=unknown error_code=SECURITY_INVALID_CREDENTIALS message=Security violation (INVALID_CREDENTIALS): Invalid email or password provided. operation=None recoverable=False severity=critical user_id=None
{"email": "<EMAIL>", "success": false, "method": "email_password", "event_type": "business_event", "event": "login_attempt", "timestamp": "2025-06-27T20:44:10.222000Z", "level": "info"}
login_failure                  correlation_id=req_e8e881814c25 email=<EMAIL> error_code=SECURITY_INVALID_CREDENTIALS
retry_exhausted                attempts=1 correlation_id=req_e8e881814c25 error=Security violation (INVALID_CREDENTIALS): Invalid email or password provided.       
{"method": "POST", "path": "/api/v1/login", "status_code": 500, "duration_ms": 5.***************, "event": "http_request", "timestamp": "2025-06-27T20:44:10.222000Z", "level": "info"}
{"method": "POST", "path": "/api/v1/login", "status_code": 500, "duration_ms": 5.***************, "event": "http_request", "timestamp": "2025-06-27T20:44:10.222000Z", "level": "info"}
________________ TestProtectedEndpoints.test_me_endpoint_success ________________ 
tests\test_routes_comprehensive.py:196: in test_me_endpoint_success
    assert "correlation_id" in data
E   AssertionError: assert 'correlation_id' in {'email': '<EMAIL>', 'id': 17, 'is_active': True, 'is_google_account': False}
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=req_9a8e0c423cb1
 email=<EMAIL> user_agent=python-httpx/0.28.1
Attempting to register new user email=<EMAIL>
User registered successfully   email=<EMAIL> user_id=17     
{"user_id": "17", "method": "email_password", "event_type": "business_event", "event": "user_registered", "timestamp": "2025-06-27T20:44:10.541820Z", "level": "info"}
user_registration_success      correlation_id=req_9a8e0c423cb1 email=<EMAIL> user_id=17
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 299.**************, "event": "http_request", "timestamp": "2025-06-27T20:44:10.541820Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 299.**************, "event": "http_request", "timestamp": "2025-06-27T20:44:10.542933Z", "level": "info"}
login_attempt                  client_ip=127.0.0.1 correlation_id=req_d5ade5995eb6
 email=<EMAIL>
Login attempt                  email=<EMAIL>
User logged in successfully    email=<EMAIL> user_id=17     
{"email": "<EMAIL>", "success": true, "method": "email_password", "event_type": "business_event", "event": "login_attempt", "timestamp": "2025-06-27T20:44:10.847873Z", "level": "info"}
login_success                  correlation_id=req_d5ade5995eb6 email=<EMAIL>
{"method": "POST", "path": "/api/v1/login", "status_code": 200, "duration_ms": 303.9436340332031, "event": "http_request", "timestamp": "2025-06-27T20:44:10.848871Z", "level": "info"}
{"method": "POST", "path": "/api/v1/login", "status_code": 200, "duration_ms": 304.94213104248047, "event": "http_request", "timestamp": "2025-06-27T20:44:10.848871Z", "level": "info"}
user_authenticated             correlation_id=req_c3183b194ad5 user_id=17
user_info_accessed             correlation_id=req_c3183b194ad5 user_id=17
{"method": "GET", "path": "/api/v1/me", "status_code": 200, "duration_ms": 11.461257934570312, "event": "http_request", "timestamp": "2025-06-27T20:44:10.860333Z", "level": "info"}
{"method": "GET", "path": "/api/v1/me", "status_code": 200, "duration_ms": 12.45880126953125, "event": "http_request", "timestamp": "2025-06-27T20:44:10.861330Z", "level": "info"}
_____________ TestProtectedEndpoints.test_me_endpoint_invalid_token _____________ 
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jws.py:176: in _load
    signing_input, crypto_segment = jwt.rsplit(b".", 1)
E   ValueError: not enough values to unpack (expected 2, got 1)

During handling of the above exception, another exception occurred:
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jwt.py:142: in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jws.py:70: in verify
    header, payload, signing_input, signature = _load(token)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jws.py:180: in _load
    raise JWSError("Not enough segments")
E   jose.exceptions.JWSError: Not enough segments

During handling of the above exception, another exception occurred:
src\shared\security.py:68: in verify_access_token
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])  # type: ignore[no-any-return]
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jwt.py:144: in decode
    raise JWTError(e)
E   jose.exceptions.JWTError: Not enough segments

The above exception was the direct cause of the following exception:
src\api\routes.py:87: in get_current_user_enhanced
    payload = verify_access_token(credentials.credentials)
src\shared\security.py:71: in verify_access_token
    raise ValueError("Invalid or expired token") from e
E   ValueError: Invalid or expired token

During handling of the above exception, another exception occurred:
tests\test_routes_comprehensive.py:208: in test_me_endpoint_invalid_token
    response = await async_client.get(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1768: in get
    return await self.request(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects) 
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1629: in send
    response = await self._send_handling_auth(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_transports\asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\errors.py:187: in __call__
    raise exc
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\errors.py:165: in __call__
    await self.app(scope, receive, _send)
src\shared\observability\monitoring.py:576: in __call__
    await self.app(scope, receive, send_wrapper)
src\shared\observability\monitoring.py:576: in __call__
    await self.app(scope, receive, send_wrapper)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:53: in wrapped_app
    raise exc
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:42: in wrapped_app
    await app(scope, receive, sender)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:715: in __call__
    await self.middleware_stack(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:735: in app
    await route.handle(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:288: in handle
    await self.app(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:76: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)        
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:53: in wrapped_app
    raise exc
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:42: in wrapped_app
    await app(scope, receive, sender)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:73: in app
    response = await f(request)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py:291: in app
    solved_result = await solve_dependencies(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\dependencies\utils.py:638: in solve_dependencies
    solved = await call(**solved_result.values)
src\api\routes.py:150: in get_current_user_enhanced
    raise SecurityError(
src\shared\errors\exceptions.py:202: in __init__
    super().__init__(
src\shared\errors\exceptions.py:98: in __init__
    self._log_error()
src\shared\errors\exceptions.py:120: in _log_error
    "correlation_id": self.context.correlation_id,
E   AttributeError: 'dict' object has no attribute 'correlation_id'
----------------------------- Captured stdout call ------------------------------ 
unexpected_authentication_error correlation_id=req_10fdf9787322 error=Invalid or expired token
unhandled_exception            correlation_id=err_1751057050892 error='dict' object has no attribute 'correlation_id' error_type=AttributeError method=GET path=/api/v1/me
_____________ TestProtectedEndpoints.test_me_endpoint_expired_token _____________
src\shared\security.py:68: in verify_access_token
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])  # type: ignore[no-any-return]
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jwt.py:157: in decode
    _validate_claims(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jwt.py:481: in _validate_claims
    _validate_exp(claims, leeway=leeway)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jwt.py:314: in _validate_exp
    raise ExpiredSignatureError("Signature has expired.")
E   jose.exceptions.ExpiredSignatureError: Signature has expired.

The above exception was the direct cause of the following exception:
src\api\routes.py:87: in get_current_user_enhanced
    payload = verify_access_token(credentials.credentials)
src\shared\security.py:71: in verify_access_token
    raise ValueError("Invalid or expired token") from e
E   ValueError: Invalid or expired token

During handling of the above exception, another exception occurred:
tests\test_routes_comprehensive.py:228: in test_me_endpoint_expired_token
    response = await async_client.get(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1768: in get
    return await self.request(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects) 
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1629: in send
    response = await self._send_handling_auth(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_transports\asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\errors.py:187: in __call__
    raise exc
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\errors.py:165: in __call__
    await self.app(scope, receive, _send)
src\shared\observability\monitoring.py:576: in __call__
    await self.app(scope, receive, send_wrapper)
src\shared\observability\monitoring.py:576: in __call__
    await self.app(scope, receive, send_wrapper)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)      
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:53: in wrapped_app
    raise exc
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:42: in wrapped_app
    await app(scope, receive, sender)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:715: in __call__
    await self.middleware_stack(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:735: in app
    await route.handle(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:288: in handle
    await self.app(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:76: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)        
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:53: in wrapped_app
    raise exc
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:42: in wrapped_app
    await app(scope, receive, sender)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:73: in app
    response = await f(request)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py:291: in app
    solved_result = await solve_dependencies(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\dependencies\utils.py:638: in solve_dependencies
    solved = await call(**solved_result.values)
src\api\routes.py:150: in get_current_user_enhanced
    raise SecurityError(
src\shared\errors\exceptions.py:202: in __init__
    super().__init__(
src\shared\errors\exceptions.py:98: in __init__
    self._log_error()
src\shared\errors\exceptions.py:120: in _log_error
    "correlation_id": self.context.correlation_id,
E   AttributeError: 'dict' object has no attribute 'correlation_id'
----------------------------- Captured stdout call ------------------------------ 
unexpected_authentication_error correlation_id=req_ee6535aa4b41 error=Invalid or expired token
unhandled_exception            correlation_id=err_1751057051473 error='dict' object has no attribute 'correlation_id' error_type=AttributeError method=GET path=/api/v1/me
________ TestPasswordResetEndpoints.test_password_reset_request_success _________
tests\test_routes_comprehensive.py:262: in test_password_reset_request_success    
    assert response.status_code == 200
E   assert 202 == 200
E    +  where 202 = <Response [202 Accepted]>.status_code
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=req_1666f1b9c1a6
 email=<EMAIL> user_agent=python-httpx/0.28.1
Attempting to register new user email=<EMAIL>
User registered successfully   email=<EMAIL> user_id=18     
{"user_id": "18", "method": "email_password", "event_type": "business_event", "event": "user_registered", "timestamp": "2025-06-27T20:44:12.359415Z", "level": "info"}
user_registration_success      correlation_id=req_1666f1b9c1a6 email=<EMAIL> user_id=18
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 323.17447662353516, "event": "http_request", "timestamp": "2025-06-27T20:44:12.359415Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 323.17447662353516, "event": "http_request", "timestamp": "2025-06-27T20:44:12.359415Z", "level": "info"}
password_reset_requested       client_ip=127.0.0.1 correlation_id=req_a660c32cbac9
 email=<EMAIL>
{"method": "POST", "path": "/api/v1/password-reset/request", "status_code": 202, "duration_ms": 8.02302360534668, "event": "http_request", "timestamp": "2025-06-27T20:44:12.368438Z", "level": "info"}
{"method": "POST", "path": "/api/v1/password-reset/request", "status_code": 202, "duration_ms": 8.02302360534668, "event": "http_request", "timestamp": "2025-06-27T20:44:12.368438Z", "level": "info"}
____ TestPasswordResetEndpoints.test_password_reset_request_nonexistent_user ____ 
tests\test_routes_comprehensive.py:275: in test_password_reset_request_nonexistent_user
    assert response.status_code == 200
E   assert 202 == 200
E    +  where 202 = <Response [202 Accepted]>.status_code
----------------------------- Captured stdout call ------------------------------ 
password_reset_requested       client_ip=127.0.0.1 correlation_id=req_e7b311d504f1
 email=<EMAIL>
{"method": "POST", "path": "/api/v1/password-reset/request", "status_code": 202, "duration_ms": 5.985736846923828, "event": "http_request", "timestamp": "2025-06-27T20:44:12.395940Z", "level": "info"}
{"method": "POST", "path": "/api/v1/password-reset/request", "status_code": 202, "duration_ms": 5.985736846923828, "event": "http_request", "timestamp": "2025-06-27T20:44:12.395940Z", "level": "info"}
___________ TestCorrelationIdHandling.test_correlation_id_in_headers ____________ 
tests\test_routes_comprehensive.py:294: in test_correlation_id_in_headers
    assert data["correlation_id"] == correlation_id
E   KeyError: 'correlation_id'
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=test-correlation-123 email=<EMAIL> user_agent=python-httpx/0.28.1
Attempting to register new user email=<EMAIL>
User registered successfully   email=<EMAIL> user_id=19     
{"user_id": "19", "method": "email_password", "event_type": "business_event", "event": "user_registered", "timestamp": "2025-06-27T20:44:12.708221Z", "level": "info"}
user_registration_success      correlation_id=test-correlation-123 email=<EMAIL> user_id=19
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 288.*************, "event": "http_request", "timestamp": "2025-06-27T20:44:12.708221Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 288.*************, "event": "http_request", "timestamp": "2025-06-27T20:44:12.708221Z", "level": "info"}
____________ TestCorrelationIdHandling.test_correlation_id_generated ____________ 
tests\test_routes_comprehensive.py:306: in test_correlation_id_generated
    assert "correlation_id" in data
E   AssertionError: assert 'correlation_id' in {'email': '<EMAIL>', 'id': 20, 'is_active': True, 'is_google_account': False}
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=req_a3707008590e
 email=<EMAIL> user_agent=python-httpx/0.28.1
Attempting to register new user email=<EMAIL>
User registered successfully   email=<EMAIL> user_id=20     
{"user_id": "20", "method": "email_password", "event_type": "business_event", "event": "user_registered", "timestamp": "2025-06-27T20:44:13.011867Z", "level": "info"}
user_registration_success      correlation_id=req_a3707008590e email=<EMAIL> user_id=20
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 283.*************, "event": "http_request", "timestamp": "2025-06-27T20:44:13.011867Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 283.*************, "event": "http_request", "timestamp": "2025-06-27T20:44:13.011867Z", "level": "info"}
________________ TestErrorHandling.test_database_error_handling _________________ 
tests\test_routes_comprehensive.py:327: in test_database_error_handling
    assert "system_error" in error_data["code"]
E   AssertionError: assert 'system_error' in 'INTERNAL_ERROR'
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=req_81479bb31cfa
 email=<EMAIL> user_agent=python-httpx/0.28.1
unexpected_registration_error  correlation_id=req_81479bb31cfa email=<EMAIL> error=Database connection failed
retry_exhausted                attempts=1 correlation_id=req_81479bb31cfa error='str' object has no attribute 'value'
unexpected_error               correlation_id=req_81479bb31cfa error='str' object has no attribute 'value' operation=user_registration
enterprise_error               category=infrastructure correlation_id=req_81479bb31cfa error_code=INTERNAL_ERROR message=Unexpected error in user_registration: 'str' object has no attribute 'value' operation=user_registration recoverable=False severity=high user_id=None
{"method": "POST", "path": "/api/v1/register", "status_code": 500, "duration_ms": 5.046844482421875, "event": "http_request", "timestamp": "2025-06-27T20:44:13.036910Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 500, "duration_ms": 5.046844482421875, "event": "http_request", "timestamp": "2025-06-27T20:44:13.036910Z", "level": "info"}
______________ TestErrorHandling.test_email_service_error_handling ______________ 
tests\test_routes_comprehensive.py:351: in test_email_service_error_handling      
    assert response.status_code in [200, 500]  # Depending on implementation      
E   assert 400 in [200, 500]
E    +  where 400 = <Response [400 Bad Request]>.status_code
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=req_e9f86a7ad845
 email=<EMAIL> user_agent=python-httpx/0.28.1
Attempting to register new user email=<EMAIL>
User registered successfully   email=<EMAIL> user_id=21     
{"user_id": "21", "method": "email_password", "event_type": "business_event", "event": "user_registered", "timestamp": "2025-06-27T20:44:13.350141Z", "level": "info"}
user_registration_success      correlation_id=req_e9f86a7ad845 email=<EMAIL> user_id=21
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 293.6384677886963, "event": "http_request", "timestamp": "2025-06-27T20:44:13.350141Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 293.6384677886963, "event": "http_request", "timestamp": "2025-06-27T20:44:13.350141Z", "level": "info"}
password_reset_requested       client_ip=127.0.0.1 correlation_id=req_943102127e19
 email=<EMAIL>
unexpected_password_reset_error correlation_id=req_943102127e19 email=<EMAIL> error=SMTP server unavailable
enterprise_error               category=integration correlation_id=unknown error_code=INTEGRATION_FAILED message=Integration failure with email_service: 'str' object has no attribute 'value' operation=None recoverable=True severity=high user_id=None
retry_failed_attempt           attempt=1 correlation_id=req_943102127e19 error=Integration failure with email_service: 'str' object has no attribute 'value'        
retry_attempt                  attempt=1 correlation_id=req_943102127e19 delay=1.331809846267829
password_reset_requested       client_ip=127.0.0.1 correlation_id=req_943102127e19
 email=<EMAIL>
unexpected_password_reset_error correlation_id=req_943102127e19 email=<EMAIL> error=SMTP server unavailable
enterprise_error               category=integration correlation_id=unknown error_code=INTEGRATION_FAILED message=Integration failure with email_service: 'str' object has no attribute 'value' operation=None recoverable=True severity=high user_id=None
retry_exhausted                attempts=2 correlation_id=req_943102127e19 error=Integration failure with email_service: 'str' object has no attribute 'value'       
{"method": "POST", "path": "/api/v1/password-reset/request", "status_code": 400, "duration_ms": 1341.14408493042, "event": "http_request", "timestamp": "2025-06-27T20:44:14.692296Z", "level": "info"}
{"method": "POST", "path": "/api/v1/password-reset/request", "status_code": 400, "duration_ms": 1341.14408493042, "event": "http_request", "timestamp": "2025-06-27T20:44:14.692296Z", "level": "info"}
__________ TestPasswordValidation.test_password_validation_edge_cases ___________ 
tests\test_shared_utils.py:219: in test_password_validation_edge_cases
    assert is_valid_password("Pass123!") is True  # 8 chars, minimum
E   AssertionError: assert False is True
E    +  where False = is_valid_password('Pass123!')
_______________ TestEmailFormatting.test_format_email_edge_cases ________________ 
tests\test_shared_utils.py:271: in test_format_email_edge_cases
    assert format_email("\u200b") == ""
E   AssertionError: assert '\u200b' == ''
E     + ​
______________________ test_login_with_invalid_credentials ______________________ 
tests\api\test_api_endpoints.py:76: in test_login_with_invalid_credentials        
    assert response.status_code == 401
E   assert 500 == 401
E    +  where 500 = <Response [500 Internal Server Error]>.status_code
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=req_5113d029324f
 email=<EMAIL> user_agent=python-httpx/0.28.1
Attempting to register new user email=<EMAIL>
User registered successfully   email=<EMAIL> user_id=28     
{"user_id": "28", "method": "email_password", "event_type": "business_event", "event": "user_registered", "timestamp": "2025-06-27T20:44:18.159428Z", "level": "info"}
user_registration_success      correlation_id=req_5113d029324f email=<EMAIL> user_id=28
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 380.30266761779785, "event": "http_request", "timestamp": "2025-06-27T20:44:18.159428Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 380.30266761779785, "event": "http_request", "timestamp": "2025-06-27T20:44:18.159428Z", "level": "info"}
login_attempt                  client_ip=127.0.0.1 correlation_id=req_8b19f994b47c
 email=<EMAIL>
Login attempt                  email=<EMAIL>
Login failed: Invalid credentials email=<EMAIL>
enterprise_error               category=security correlation_id=unknown error_code=SECURITY_INVALID_CREDENTIALS message=Security violation (INVALID_CREDENTIALS): Invalid email or password provided. operation=None recoverable=False severity=critical user_id=None
{"email": "<EMAIL>", "success": false, "method": "email_password", "event_type": "business_event", "event": "login_attempt", "timestamp": "2025-06-27T20:44:18.481003Z", "level": "info"}
login_failure                  correlation_id=req_8b19f994b47c email=<EMAIL> error_code=SECURITY_INVALID_CREDENTIALS
retry_exhausted                attempts=1 correlation_id=req_8b19f994b47c error=Security violation (INVALID_CREDENTIALS): Invalid email or password provided.       
{"method": "POST", "path": "/api/v1/login", "status_code": 500, "duration_ms": 320.6460475921631, "event": "http_request", "timestamp": "2025-06-27T20:44:18.481003Z", "level": "info"}
{"method": "POST", "path": "/api/v1/login", "status_code": 500, "duration_ms": 320.6460475921631, "event": "http_request", "timestamp": "2025-06-27T20:44:18.481003Z", "level": "info"}
______________________ test_register_with_duplicate_email _______________________ 
tests\api\test_api_endpoints.py:104: in test_register_with_duplicate_email        
    assert "recovery_actions" in error_data
E   AssertionError: assert 'recovery_actions' in {'category': 'business_rule', 'code': 'BUSINESS_RULE_EMAIL_ALREADY_EXISTS', 'correlation_id': 'unknown', 'message': 'This operation is not allowed at this time.', ...}
----------------------------- Captured stdout call ------------------------------ 
registration_attempt           client_ip=127.0.0.1 correlation_id=req_2d3d482eea0e
 email=<EMAIL> user_agent=python-httpx/0.28.1
Attempting to register new user email=<EMAIL>
User registered successfully   email=<EMAIL> user_id=29     
{"user_id": "29", "method": "email_password", "event_type": "business_event", "event": "user_registered", "timestamp": "2025-06-27T20:44:18.796422Z", "level": "info"}
user_registration_success      correlation_id=req_2d3d482eea0e email=<EMAIL> user_id=29
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 298.4321117401123, "event": "http_request", "timestamp": "2025-06-27T20:44:18.796422Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 201, "duration_ms": 298.4321117401123, "event": "http_request", "timestamp": "2025-06-27T20:44:18.796422Z", "level": "info"}
registration_attempt           client_ip=127.0.0.1 correlation_id=req_8554d7849636
 email=<EMAIL> user_agent=python-httpx/0.28.1
Attempting to register new user email=<EMAIL>
User registration failed due to duplicate email email=<EMAIL>
enterprise_error               category=business_rule correlation_id=unknown error_code=BUSINESS_RULE_EMAIL_ALREADY_EXISTS message=Business rule 'EMAIL_ALREADY_EXISTS' violated: A user with the email '<EMAIL>' already exists. operation=None recoverable=True severity=medium user_id=None
retry_exhausted                attempts=1 correlation_id=req_8554d7849636 error=Business rule 'EMAIL_ALREADY_EXISTS' violated: A user with the email '<EMAIL>' already exists.
{"method": "POST", "path": "/api/v1/register", "status_code": 400, "duration_ms": 282.46164321899414, "event": "http_request", "timestamp": "2025-06-27T20:44:19.079782Z", "level": "info"}
{"method": "POST", "path": "/api/v1/register", "status_code": 400, "duration_ms": 282.46164321899414, "event": "http_request", "timestamp": "2025-06-27T20:44:19.079782Z", "level": "info"}
________________________ test_get_me_with_invalid_token _________________________ 
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jws.py:176: in _load
    signing_input, crypto_segment = jwt.rsplit(b".", 1)
E   ValueError: not enough values to unpack (expected 2, got 1)

During handling of the above exception, another exception occurred:
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jwt.py:142: in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jws.py:70: in verify
    header, payload, signing_input, signature = _load(token)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jws.py:180: in _load
    raise JWSError("Not enough segments")
E   jose.exceptions.JWSError: Not enough segments

During handling of the above exception, another exception occurred:
src\shared\security.py:68: in verify_access_token
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])  # type: ignore[no-any-return]
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jwt.py:144: in decode
    raise JWTError(e)
E   jose.exceptions.JWTError: Not enough segments

The above exception was the direct cause of the following exception:
src\api\routes.py:87: in get_current_user_enhanced
    payload = verify_access_token(credentials.credentials)
src\shared\security.py:71: in verify_access_token
    raise ValueError("Invalid or expired token") from e
E   ValueError: Invalid or expired token

During handling of the above exception, another exception occurred:
tests\api\test_api_endpoints.py:112: in test_get_me_with_invalid_token
    response = await async_client.get(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1768: in get
    return await self.request(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1540: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects) 
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1629: in send
    response = await self._send_handling_auth(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1657: in _send_handling_auth
    response = await self._send_handling_redirects(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1694: in _send_handling_redirects
    response = await self._send_single_request(request)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_client.py:1730: in _send_single_request
    response = await transport.handle_async_request(request)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\httpx\_transports\asgi.py:170: in handle_async_request
    await self.app(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\applications.py:113: in __call__
    await self.middleware_stack(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\errors.py:187: in __call__
    raise exc
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\errors.py:165: in __call__
    await self.app(scope, receive, _send)
src\shared\observability\monitoring.py:576: in __call__
    await self.app(scope, receive, send_wrapper)
src\shared\observability\monitoring.py:576: in __call__
    await self.app(scope, receive, send_wrapper)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)      
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:53: in wrapped_app
    raise exc
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:42: in wrapped_app
    await app(scope, receive, sender)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:715: in __call__
    await self.middleware_stack(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:735: in app
    await route.handle(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:288: in handle
    await self.app(scope, receive, send)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:76: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)        
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:53: in wrapped_app
    raise exc
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py:42: in wrapped_app
    await app(scope, receive, sender)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py:73: in app
    response = await f(request)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py:291: in app
    solved_result = await solve_dependencies(
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\dependencies\utils.py:638: in solve_dependencies
    solved = await call(**solved_result.values)
src\api\routes.py:150: in get_current_user_enhanced
    raise SecurityError(
src\shared\errors\exceptions.py:202: in __init__
    super().__init__(
src\shared\errors\exceptions.py:98: in __init__
    self._log_error()
src\shared\errors\exceptions.py:120: in _log_error
    "correlation_id": self.context.correlation_id,
E   AttributeError: 'dict' object has no attribute 'correlation_id'
----------------------------- Captured stdout call ------------------------------ 
unexpected_authentication_error correlation_id=req_c34d9c31ddd8 error=Invalid or expired token
unhandled_exception            correlation_id=err_1751057059101 error='dict' object has no attribute 'correlation_id' error_type=AttributeError method=GET path=/api/v1/me
=============================== warnings summary ================================ 
tests/test_auth_service.py::TestAuthService::test_login_success
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pytest_asyncio\plugin.py:761: DeprecationWarning: The event_loop fixture provided by pytest-asyncio has been redefined in
  C:\Users\<USER>\OneDrive\Documents\PythonOf\Codingagent\user-login-system\tests\conftest.py:384
  Replacing the event_loop fixture with a custom implementation is deprecated     
  and will lead to errors in the future.
  If you want to request an asyncio event loop with a scope other than function   
  scope, use the "scope" argument to the asyncio mark when marking the tests.     
  If you want to return different types of event loops, use the event_loop_policy 
  fixture.

    warnings.warn(

tests/test_domain_auth.py: 10 warnings
tests/test_routes_comprehensive.py: 2 warnings
tests/test_shared_utils.py: 6 warnings
tests/api/test_api_endpoints.py: 1 warning
tests/domain/test_security.py: 2 warnings
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\jose\jwt.py:311: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).
    now = timegm(datetime.utcnow().utctimetuple())

tests/test_domain_auth.py::TestJWTTokens::test_create_access_token_default_expiry 
  C:\Users\<USER>\OneDrive\Documents\PythonOf\Codingagent\user-login-system\tests\test_domain_auth.py:203: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).
    current_timestamp = datetime.utcnow().timestamp()

tests/test_domain_auth.py::TestJWTTokens::test_create_access_token_custom_expiry  
  C:\Users\<USER>\OneDrive\Documents\PythonOf\Codingagent\user-login-system\tests\test_domain_auth.py:218: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).
    expected_exp = datetime.utcnow() + expires_delta

tests/domain/test_security.py::test_jwt_token_expiration
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pytest_asyncio\plugin.py:818: DeprecationWarning: pytest-asyncio detected an unclosed event loop when tearing down the event_loop
  fixture: <ProactorEventLoop running=False closed=False debug=False>
  pytest-asyncio will close the event loop for you, but future versions of the    
  library will no longer do so. In order to ensure compatibility with future      
  versions, please make sure that:
      1. Any custom "event_loop" fixture properly closes the loop after yielding it
      2. The scopes of your custom "event_loop" fixtures do not overlap
      3. Your code does not modify the event loop in async fixtures or tests      

    warnings.warn(

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html

---------- coverage: platform win32, python 3.12.0-final-0 -----------
Name                                     Stmts   Miss Branch BrPart   Cover   Missing
-------------------------------------------------------------------------------------
src\__init__.py                              0      0      0      0 100.00%       
src\api\__init__.py                          0      0      0      0 100.00%       
src\api\routes.py                          182     90     22      3  47.55%   94-125, 134-139, 182, 215, 232-242, 251-261, 278-279, 340-375, 444, 465-515, 543-629, 656-691, 700-707
src\api\routes_legacy.py                    34     34      0      0   0.00%   1-70
src\api\schemas.py                          22      0      0      0 100.00%       
src\application\__init__.py                  0      0      0      0 100.00%       
src\application\auth_service.py             29      2      6      1  91.43%   50-52
src\application\user_service.py             40      0      4      0 100.00%       
src\config\__init__.py                       0      0      0      0 100.00%       
src\config\settings.py                     320     69     74     24  70.81%   109, 118, 123, 169, 171, 177-181, 186, 225-226, 277, 279, 314, 316, 319, 321, 376, 432, 444, 451, 460->467, 462, 464, 467->472, 469, 473-474, 480, 484, 496, 514->516, 520->539, 524, 535-537, 549-550, 554-558, 570-571, 582-624, 629
src\domain\__init__.py                       0      0      0      0 100.00%       
src\domain\auth.py                          31      0      2      0 100.00%       
src\domain\user.py                          12      0      0      0 100.00%       
src\enhanced_routes.py                     191    191     22      0   0.00%   16-750
src\enterprise_test_suite.py               288    288     44      0   0.00%   16-755
src\infrastructure\__init__.py               0      0      0      0 100.00%       
src\infrastructure\db.py                    11      2      0      0  81.82%   19-20
src\infrastructure\email_service.py         24     11      4      1  57.14%   24-41
src\main.py                                 33      9      0      0  72.73%   30-40, 73
src\main_backup.py                         201    201     34      0   0.00%   17-546
src\shared\__init__.py                       0      0      0      0 100.00%       
src\shared\errors\__init__.py                0      0      0      0 100.00%       
src\shared\errors\exceptions.py            183     30     30      9  77.93%   246-251, 259-263, 272-274, 279-283, 294-295, 297, 302-303, 332->374, 335-344, 367, 415
src\shared\observability\__init__.py         0      0      0      0 100.00%       
src\shared\observability\monitoring.py     280    142     40      7  45.94%   139-146, 150-203, 207-227, 231, 234, 237->239, 240, 242->244, 247, 252-269, 284-285, 294-325, 334-363, 372-410, 462, 481, 488, 492-526, 548-549, 591-643, 650, 663-681, 694
src\shared\security.py                      45     11      8      0  64.15%   27, 31-39, 43
src\shared\utils.py                         29      0      4      0 100.00%       
-------------------------------------------------------------------------------------
TOTAL                                     1955   1080    294     45  42.15%       
Coverage HTML written to dir htmlcov
Coverage XML written to file coverage.xml

FAIL Required test coverage of 90% not reached. Total coverage: 42.15%
============================ short test summary info ============================
FAILED tests/test_auth_service.py::TestAuthService::test_login_inactive_user - assert 'account_disabled' in 'BUSINESS_RULE_ACCOUNT_INACTIVE'
FAILED tests/test_auth_service.py::TestAuthService::test_verify_token_success - AttributeError: 'AuthService' object has no attribute 'verify_token'
FAILED tests/test_auth_service.py::TestAuthService::test_verify_token_invalid - AttributeError: 'AuthService' object has no attribute 'verify_token'
FAILED tests/test_auth_service.py::TestAuthService::test_verify_token_expired - AttributeError: 'AuthService' object has no attribute 'verify_token'
FAILED tests/test_auth_service.py::TestAuthService::test_verify_token_user_not_found - AttributeError: 'AuthService' object has no attribute 'verify_token'
FAILED tests/test_auth_service.py::TestAuthService::test_refresh_token_success - AttributeError: 'AuthService' object has no attribute 'refresh_token'
FAILED tests/test_auth_service.py::TestAuthService::test_refresh_token_invalid - AttributeError: 'AuthService' object has no attribute 'refresh_token'
FAILED tests/test_auth_service.py::TestAuthService::test_logout_success - AttributeError: 'AuthService' object has no attribute 'logout'
FAILED tests/test_auth_service.py::TestAuthService::test_logout_invalid_token - AttributeError: 'AuthService' object has no attribute 'logout'
FAILED tests/test_auth_service.py::TestAuthService::test_change_password_success - AttributeError: 'AuthService' object has no attribute 'change_password'
FAILED tests/test_auth_service.py::TestAuthService::test_change_password_wrong_old_password - AttributeError: 'AuthService' object has no attribute 'change_password'
FAILED tests/test_auth_service.py::TestAuthService::test_reset_password_success - src.shared.errors.exceptions.BusinessRuleViolation: Business rule 'USER_NOT_F...  
FAILED tests/test_auth_service.py::TestAuthService::test_reset_password_user_not_found - src.shared.errors.exceptions.BusinessRuleViolation: Business rule 'USER_NOT_F...
FAILED tests/test_auth_service.py::TestAuthService::test_login_tracking - AssertionError: Expected 'track_login_attempt' to have been called.
FAILED tests/test_auth_service.py::TestAuthService::test_rate_limiting - AttributeError: <class 'src.application.auth_service.AuthService'> does not h...
FAILED tests/test_auth_service.py::TestAuthService::test_security_event_logging - AssertionError: Expected 'warning' to have been called.
FAILED tests/test_domain_auth.py::TestPasswordVerification::test_verify_password_empty_hash - passlib.exc.UnknownHashError: hash could not be identified
FAILED tests/test_domain_auth.py::TestPasswordVerification::test_verify_password_invalid_hash_format - passlib.exc.UnknownHashError: hash could not be identified   
FAILED tests/test_domain_auth.py::TestJWTTokens::test_create_access_token_default_expiry - assert 1751060634 > 1751071434.16147
FAILED tests/test_domain_auth.py::TestJWTTokens::test_create_access_token_custom_expiry - assert 14400.244174 < 60
FAILED tests/test_email_service.py::TestEmailService::test_send_email_success - RuntimeError: GMAIL_USER environment variable is not set. Please configure a ...    
FAILED tests/test_email_service.py::TestEmailService::test_send_email_smtp_error - AssertionError: Regex pattern did not match.
FAILED tests/test_email_service.py::TestEmailService::test_send_email_empty_gmail_pass - RuntimeError: GMAIL_USER environment variable is not set. Please configure a ...
FAILED tests/test_email_service.py::TestEmailService::test_send_email_logging_success - RuntimeError: GMAIL_USER environment variable is not set. Please configure a ...
FAILED tests/test_email_service.py::TestEmailService::test_send_email_logging_error - AssertionError: expected call not found.
FAILED tests/test_email_service.py::TestEmailService::test_send_email_special_characters - RuntimeError: GMAIL_USER environment variable is not set. Please configure a ...
FAILED tests/test_email_service.py::TestEmailService::test_send_email_long_content - RuntimeError: GMAIL_USER environment variable is not set. Please configure a ...
FAILED tests/test_email_service.py::TestEmailService::test_send_email_multiple_recipients_format - RuntimeError: GMAIL_USER environment variable is not set. Please configure a ...
FAILED tests/test_email_service.py::TestEmailService::test_send_email_concurrent - RuntimeError: GMAIL_USER environment variable is not set. Please configure a ... 
FAILED tests/test_email_service.py::TestEmailService::test_send_email_timeout_error - RuntimeError: GMAIL_USER environment variable is not set. Please configure a ...
FAILED tests/test_email_service.py::TestEmailService::test_send_email_connection_error - RuntimeError: GMAIL_USER environment variable is not set. Please configure a ...
FAILED tests/test_email_service.py::TestEmailService::test_send_email_authentication_error - TypeError: SMTPResponseException.__init__() got some positional-only argument...
FAILED tests/test_email_service.py::TestEmailService::test_send_email_message_structure - RuntimeError: GMAIL_USER environment variable is not set. Please configure a ...
FAILED tests/test_email_service.py::TestEmailServiceConfiguration::test_missing_credentials_warning - AssertionError: expected call not found.
FAILED tests/test_email_service.py::TestEmailServiceConfiguration::test_partial_credentials_warning - AssertionError: expected call not found.
FAILED tests/test_routes_comprehensive.py::TestRegistrationEndpoint::test_register_success - AssertionError: assert 'correlation_id' in {'email': 'testuser_7db046e1@examp...
FAILED tests/test_routes_comprehensive.py::TestRegistrationEndpoint::test_register_duplicate_email - AssertionError: assert 'BUSINESS_RUL...LREADY_EXISTS' == 'EMAIL_ALREADY_EXISTS'
FAILED tests/test_routes_comprehensive.py::TestRegistrationEndpoint::test_register_weak_password - assert 400 == 422
FAILED tests/test_routes_comprehensive.py::TestRegistrationEndpoint::test_register_when_disabled - assert 201 == 400
FAILED tests/test_routes_comprehensive.py::TestLoginEndpoint::test_login_invalid_credentials - assert 500 == 401
FAILED tests/test_routes_comprehensive.py::TestLoginEndpoint::test_login_nonexistent_user - assert 500 == 401
FAILED tests/test_routes_comprehensive.py::TestProtectedEndpoints::test_me_endpoint_success - AssertionError: assert 'correlation_id' in {'email': 'testuser_70bf1af6@examp...
FAILED tests/test_routes_comprehensive.py::TestProtectedEndpoints::test_me_endpoint_invalid_token - AttributeError: 'dict' object has no attribute 'correlation_id' 
FAILED tests/test_routes_comprehensive.py::TestProtectedEndpoints::test_me_endpoint_expired_token - AttributeError: 'dict' object has no attribute 'correlation_id' 
FAILED tests/test_routes_comprehensive.py::TestPasswordResetEndpoints::test_password_reset_request_success - assert 202 == 200
FAILED tests/test_routes_comprehensive.py::TestPasswordResetEndpoints::test_password_reset_request_nonexistent_user - assert 202 == 200
FAILED tests/test_routes_comprehensive.py::TestCorrelationIdHandling::test_correlation_id_in_headers - KeyError: 'correlation_id'
FAILED tests/test_routes_comprehensive.py::TestCorrelationIdHandling::test_correlation_id_generated - AssertionError: assert 'correlation_id' in {'email': 'testuser_84d9375e@examp...
FAILED tests/test_routes_comprehensive.py::TestErrorHandling::test_database_error_handling - AssertionError: assert 'system_error' in 'INTERNAL_ERROR'
FAILED tests/test_routes_comprehensive.py::TestErrorHandling::test_email_service_error_handling - assert 400 in [200, 500]
FAILED tests/test_shared_utils.py::TestPasswordValidation::test_password_validation_edge_cases - AssertionError: assert False is True
FAILED tests/test_shared_utils.py::TestEmailFormatting::test_format_email_edge_cases - AssertionError: assert '\u200b' == ''
FAILED tests/api/test_api_endpoints.py::test_login_with_invalid_credentials - assert 500 == 401
FAILED tests/api/test_api_endpoints.py::test_register_with_duplicate_email - AssertionError: assert 'recovery_actions' in {'category': 'business_rule', 'c...       
FAILED tests/api/test_api_endpoints.py::test_get_me_with_invalid_token - AttributeError: 'dict' object has no attribute 'correlation_id'
================== 55 failed, 75 passed, 25 warnings in 51.78s ==================