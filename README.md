# User Login System

<!--
IDEAL WORKFLOW ANNOTATION

1. Requirements & Threat Model
   - Clarify requirements and security context (CCR-001, CCR-002)
   - Document threat model in code and PRD
2. Environment Setup
   - Clone repo, create venv, install dependencies
   - Configure .env with secrets (never hardcode!)
3. Development
   - Use DDD structure: domain, application, infrastructure, api, shared
   - Implement features with type hints, validation, and error handling
   - Write tests for all new code (unit, integration, edge)
   - Use dependency injection for testability
   - Commit early, commit often
4. Quality Gates
   - Run mypy (strict), ruff, bandit, pytest (with coverage)
   - Ensure 90%+ coverage, zero static analysis errors
   - Fix all test failures before merging
5. Documentation & Deployment
   - Update README, PRD, and OpenAPI docs
   - Prepare deployment (env, health checks, resource cleanup)
6. Iteration
   - Use feedback loop: review, refine, retest
   - Document known limitations and TODOs
-->

This project implements a user login system for small business websites, allowing users to create accounts, log in, and reset their passwords securely. The system is designed with enterprise-level quality standards, focusing on security, performance, reliability, and maintainability.

## Project Structure

The project is organized into several directories, each serving a specific purpose:

- **src/**: Contains the main application code.
  - **domain/**: Defines the core business logic and entities.
    - `user.py`: Represents a user in the system, including properties and methods for user-related operations.
    - `auth.py`: Contains authentication logic, including password hashing and token generation.
  - **application/**: Implements services that interact with the domain layer.
    - `user_service.py`: Provides methods for user registration and management.
    - `auth_service.py`: Handles user authentication and session management.
  - **infrastructure/**: Manages external integrations and data storage.
    - `db.py`: Contains database connection and interaction logic.
    - `email_service.py`: Provides functionality for sending emails, such as password reset links.
  - **api/**: Defines the API endpoints and request/response schemas.
    - `routes.py`: Legacy/minimal API endpoints (MVP, reference only)
    - `enhanced_routes.py`: Main production API layer (enterprise features, recommended for all deployments)
    - `schemas.py`: Defines data validation and serialization models using Pydantic.
  - **shared/**: Contains cross-cutting concerns and utility functions.
    - `security.py`: Includes security utilities for input validation and checks.
    - `utils.py`: Provides general utility functions for the application.

- **tests/**: Contains unit tests for the application components.
  - `test_user.py`: Tests for the User class and its methods.
  - `test_auth.py`: Tests for authentication logic.
  - `test_api.py`: Tests for API endpoints.

- **pyproject.toml**: Configuration file specifying dependencies and tools for the project.

- **.env.example**: Example environment configuration file with placeholders for sensitive information.

## Setup Instructions

1. Clone the repository:
   ```
   git clone <repository-url>
   cd user-login-system
   ```

2. Create a virtual environment and activate it:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows use `venv\Scripts\activate`
   ```

3. Install the dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Configure your environment variables by copying `.env.example` to `.env` and filling in the required values.

## Usage

To run the application, use the following command:
```
uvicorn src.api.routes:app --reload
```

## Testing

To run the tests, use:
```
pytest
```

## Architecture Overview

The user login system is designed with a clear separation of concerns, following domain-driven design principles. The architecture allows for easy maintenance and scalability, ensuring that the system can grow with the needs of the business.

## Security Considerations

The system implements various security measures, including input validation, password hashing, and secure token generation, to protect user data and prevent common vulnerabilities.

## License

This project is licensed under the MIT License. See the LICENSE file for more details.