"""
Enterprise Configuration Management System
========================================
Implements Gold Standard configuration patterns with environment-specific settings,
secrets management, and comprehensive validation.

FEATURES:
- Type-safe configuration with Pydantic BaseSettings
- Environment-specific configuration inheritance
- Secure secrets management with multiple backends
- Runtime configuration validation
- Feature flags and operational toggles
"""

import os
import secrets
import datetime
from typing import Optional, Dict, Any, List
from enum import Enum
from pathlib import Path
import structlog
# Near the top of src/config/settings.py
# Import BaseSettings from its new home
from pydantic_settings import BaseSettings

# Import everything else from the core pydantic library
from pydantic import (
    Field,
    EmailStr,
    AnyHttpUrl,
    SecretStr,
    field_validator,
    model_validator,
    ValidationInfo
)

from dotenv import load_dotenv
load_dotenv()

logger = structlog.get_logger()

class Environment(str, Enum):
    """Deployment environments with specific configurations."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

class LogLevel(str, Enum):
    """Logging levels for application configuration."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class DatabaseType(str, Enum):
    """Supported database types."""
    SQLITE = "sqlite"
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"

class SecurityConfig(BaseSettings):
    """
    Security-related configuration with enterprise-grade defaults.
    
    SECURITY: All sensitive values use SecretStr to prevent accidental logging
    VALIDATION: Comprehensive validation for security parameters
    COMPLIANCE: Configurable security levels for different environments
    """
    
    # JWT Configuration
    jwt_secret_key: SecretStr = Field(..., description="JWT signing secret key")
    jwt_algorithm: str = Field(default="RS256", description="JWT signing algorithm")
    jwt_access_token_expire_minutes: int = Field(default=15, ge=5, le=60)
    jwt_refresh_token_expire_days: int = Field(default=7, ge=1, le=30)
    
    # Password Policy
    password_min_length: int = Field(default=12, ge=8)
    password_require_uppercase: bool = Field(default=True)
    password_require_lowercase: bool = Field(default=True)
    password_require_digits: bool = Field(default=True)
    password_require_special: bool = Field(default=True)
    
    # Account Security
    max_login_attempts: int = Field(default=5, ge=3, le=10)
    account_lockout_duration_minutes: int = Field(default=30, ge=15)
    session_timeout_minutes: int = Field(default=480, ge=60)  # 8 hours default
    
    # API Security
    enable_rate_limiting: bool = Field(default=True)
    rate_limit_requests_per_minute: int = Field(default=100, ge=10)
    enable_cors: bool = Field(default=True)
    cors_origins: List[str] = Field(default_factory=list)
    cors_allow_credentials: bool = Field(default=True)
    
    # Encryption
    encryption_key: Optional[SecretStr] = Field(default=None)
    
    model_config = {
        "env_prefix": "SECURITY_"
    }

    @field_validator('jwt_secret_key')
    def validate_jwt_secret_strength(cls, v):
        """Ensure JWT secret is sufficiently strong."""
        secret_str = v.get_secret_value()
        if len(secret_str) < 32:
            raise ValueError("JWT secret key must be at least 32 characters long")
        return v

    @field_validator('cors_origins')
    def validate_cors_origins(cls, v, values):
        """Validate CORS origins based on environment."""
        environment = os.getenv("ENVIRONMENT", "development")
        
        if environment == "production" and not v:
            raise ValueError("CORS origins must be specified in production")
        
        # Validate URL format
        for origin in v:
            if not origin.startswith(('http://', 'https://')):
                raise ValueError(f"Invalid CORS origin format: {origin}")
        
        return v

class DatabaseConfig(BaseSettings):
    """
    Database configuration with connection pooling and performance tuning.
    
    PERFORMANCE: Connection pooling and timeout configuration
    SECURITY: SSL configuration and credential management
    SCALABILITY: Read/write split support for future scaling
    """
    
    # Database Connection
    type: DatabaseType = Field(default=DatabaseType.SQLITE)
    host: str = Field(default="localhost")
    port: int = Field(default=5432, ge=1, le=65535)
    name: str = Field(default="user_login_system")
    username: str = Field(default="app_user")
    password: SecretStr = Field(default=SecretStr("changeme"))
    
    # Connection Pooling
    pool_size: int = Field(default=20, ge=5, le=100)
    max_overflow: int = Field(default=10, ge=0, le=50)
    pool_timeout: int = Field(default=30, ge=5)
    pool_recycle: int = Field(default=3600, ge=300)  # 1 hour
    
    # Connection Security
    ssl_mode: str = Field(default="prefer")
    ssl_cert_path: Optional[Path] = Field(default=None)
    ssl_key_path: Optional[Path] = Field(default=None)
    ssl_ca_path: Optional[Path] = Field(default=None)
    
    # Performance
    query_timeout: int = Field(default=30, ge=5)
    connection_timeout: int = Field(default=10, ge=5)
    
    model_config = {
        "env_prefix": "DATABASE_"
    }

    @field_validator('port')
    def validate_port_for_db_type(cls, v, info: ValidationInfo):
        """Set default ports based on database type."""
        db_type = info.data.get('type')
        if db_type == DatabaseType.POSTGRESQL and v == 5432:
            return 5432
        elif db_type == DatabaseType.MYSQL and v == 5432:
            return 3306
        return v

    @property
    def url(self) -> str:
        """Generate database URL from components."""
        if self.type == DatabaseType.SQLITE:
            return "sqlite+aiosqlite:///./user_login.db"
        
        password = self.password.get_secret_value()
        return f"{self.type.value}+asyncpg://{self.username}:{password}@{self.host}:{self.port}/{self.name}"

    @property
    def async_url(self) -> str:
        """Generate async database URL."""
        return self.url

class RedisConfig(BaseSettings):
    """
    Redis configuration for caching and session management.
    
    PERFORMANCE: Connection pooling and timeout configuration
    SECURITY: Authentication and SSL support
    MONITORING: Health check and metrics configuration
    """
    
    host: str = Field(default="localhost")
    port: int = Field(default=6379, ge=1, le=65535)
    database: int = Field(default=0, ge=0, le=15)
    password: Optional[SecretStr] = Field(default=None)
    
    # Connection Pooling
    max_connections: int = Field(default=50, ge=10)
    socket_timeout: int = Field(default=5, ge=1)
    socket_connect_timeout: int = Field(default=5, ge=1)
    health_check_interval: int = Field(default=30, ge=10)
    
    # SSL Configuration
    ssl_enabled: bool = Field(default=False)
    ssl_cert_path: Optional[Path] = Field(default=None)
    ssl_key_path: Optional[Path] = Field(default=None)
    ssl_ca_path: Optional[Path] = Field(default=None)
    
    # Cache Configuration
    default_ttl: int = Field(default=3600, ge=60)  # 1 hour
    key_prefix: str = Field(default="uls")  # user_login_system
    
    model_config = {
        "env_prefix": "REDIS_"
    }

    @property
    def url(self) -> str:
        """Generate Redis URL from components."""
        auth = f":{self.password.get_secret_value()}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.database}"

class EmailConfig(BaseSettings):
    """
    Email service configuration with multiple provider support.
    
    RELIABILITY: Multiple provider support with failover
    SECURITY: Authentication and encryption configuration
    COMPLIANCE: Rate limiting and audit logging
    """
    
    # SMTP Configuration
    smtp_host: str = Field(default="smtp.gmail.com", description="SMTP server host")
    smtp_port: int = Field(default=587, ge=1, le=65535, description="SMTP server port")
    smtp_username: EmailStr = Field(
        default="<EMAIL>", 
        description="SMTP account username"
    )
    smtp_password: SecretStr = Field(
        default_factory=lambda: SecretStr("test-pass"), 
        description="SMTP account password"
    )
    smtp_use_tls: bool = Field(default=True, description="Use TLS for SMTP connection")
    smtp_use_ssl: bool = Field(default=False, description="Use SSL for SMTP connection")

    # Email Settings
    from_email: EmailStr = Field(
        default="<EMAIL>", 
        description="Default From address"
    )
    from_name: str = Field(default="User Login System", description="Default From name")
    reply_to: Optional[EmailStr] = Field(default=None, description="Reply-To address")

    # Rate Limiting
    max_emails_per_minute: int = Field(default=10,  ge=1,  description="Max emails per minute")
    max_emails_per_hour:   int = Field(default=100, ge=10, description="Max emails per hour")

    # Template Configuration
    template_dir: Path = Field(default=Path("templates/email"), description="Directory for email templates")
    
    model_config = {
        "env_prefix": "EMAIL_"
    }

    @field_validator('smtp_port')
    def validate_smtp_port(cls, v, info: ValidationInfo):
        """Validate SMTP port based on security settings."""
        use_ssl = info.data.get('smtp_use_ssl', False)
        use_tls = info.data.get('smtp_use_tls', True)
        
        if use_ssl and v != 465:
            logger.warning("SMTP SSL typically uses port 465", port=v)
        elif use_tls and v not in [587, 25]:
            logger.warning("SMTP TLS typically uses port 587 or 25", port=v)
        
        return v

class OAuthConfig(BaseSettings):
    """
    OAuth provider configuration for social login.
    
    SECURITY: Secure client credential management
    INTEGRATION: Multiple provider support
    COMPLIANCE: Scope and permission management
    """
    
    # Google OAuth
    google_client_id: Optional[str] = Field(default=None)
    google_client_secret: Optional[SecretStr] = Field(default=None)
    google_redirect_uri: Optional[AnyHttpUrl] = Field(default=None)
    google_scopes: List[str] = Field(default=["openid", "email", "profile"])
    
    # GitHub OAuth (for future expansion)
    github_client_id: Optional[str] = Field(default=None)
    github_client_secret: Optional[SecretStr] = Field(default=None)
    
    # OAuth Security
    state_secret: SecretStr = Field(default_factory=lambda: SecretStr(secrets.token_urlsafe(32)))
    oauth_timeout: int = Field(default=300, ge=60)  # 5 minutes
    
    model_config = {
        "env_prefix": "OAUTH_"
    }
    
    @model_validator(mode='after')
    def validate_oauth_providers(self) -> 'OAuthConfig':
        """Ensure provider secrets are present if the client ID is set."""
        if self.google_client_id and not self.google_client_secret:
            raise ValueError("Google OAuth client secret is required when client ID is provided")
        if self.google_client_secret and not self.google_client_id:
            raise ValueError("Google OAuth client ID is required when client secret is provided")
        
        if self.github_client_id and not self.github_client_secret:
            raise ValueError("GitHub OAuth client secret is required when client ID is provided")
        if self.github_client_secret and not self.github_client_id:
            raise ValueError("GitHub OAuth client ID is required when client secret is provided")
        
        return self

class ObservabilityConfig(BaseSettings):
    """
    Observability configuration for monitoring and debugging.
    
    MONITORING: Metrics and alerting configuration
    DEBUGGING: Logging and tracing configuration
    COMPLIANCE: Audit logging and retention policies
    """
    
    # Logging Configuration
    log_level: LogLevel = Field(default=LogLevel.INFO)
    log_format: str = Field(default="json")  # json, text
    log_file: Optional[Path] = Field(default=None)
    log_rotation: bool = Field(default=True)
    log_retention_days: int = Field(default=30, ge=1)
    
    # Structured Logging
    enable_structured_logging: bool = Field(default=True)
    log_correlation_id: bool = Field(default=True)
    log_request_details: bool = Field(default=True)
    log_sensitive_data: bool = Field(default=False)  # Should be False in production
    
    # Metrics
    enable_metrics: bool = Field(default=True)
    metrics_endpoint: str = Field(default="/metrics")
    prometheus_port: int = Field(default=9090, ge=1024, le=65535)
    
    # Distributed Tracing
    enable_tracing: bool = Field(default=True)
    trace_sample_rate: float = Field(default=0.1, ge=0.0, le=1.0)
    jaeger_endpoint: Optional[AnyHttpUrl] = Field(default=None)
    
    # Health Checks
    enable_health_checks: bool = Field(default=True)
    health_check_endpoint: str = Field(default="/health")
    health_check_interval: int = Field(default=30, ge=10)
    
    # APM Integration
    sentry_dsn: Optional[AnyHttpUrl] = Field(default=None) # <-- FIX: Type can be AnyHttpUrl OR None
    datadog_api_key: Optional[SecretStr] = Field(default=None)
    
    model_config = {
        "env_prefix": "OBSERVABILITY_"
    }

    @field_validator('trace_sample_rate')
    def validate_trace_sample_rate_by_environment(cls, v, values):
        """Adjust trace sample rate based on environment."""
        environment = os.getenv("ENVIRONMENT", "development")
        
        if environment == "production" and v > 0.1:
            logger.warning("High trace sample rate in production may impact performance")
        
        return v

class ApplicationConfig(BaseSettings):
    """
    Main application configuration that orchestrates all subsystems.
    
    INTEGRATION: Centralizes all configuration subsystems
    VALIDATION: Cross-system configuration validation
    ENVIRONMENT: Environment-specific configuration management
    """
    
    # Application Metadata
    app_name: str = Field(default="User Login System")
    app_version: str = Field(default="1.0.0")
    environment: Environment = Field(default=Environment.DEVELOPMENT)
    debug: bool = Field(default=False)
    
    # Server Configuration
    host: str = Field(default="0.0.0.0")
    port: int = Field(default=8000, ge=1024, le=65535)
    workers: int = Field(default=1, ge=1)
    reload: bool = Field(default=False)
    
    # Request Handling
    max_request_size: int = Field(default=10 * 1024 * 1024)  # 10MB
    request_timeout: int = Field(default=30, ge=5)
    keepalive_timeout: int = Field(default=65, ge=30)
    
    # Feature Flags
    enable_registration: bool = Field(default=True)
    enable_oauth: bool = Field(default=True)
    enable_password_reset: bool = Field(default=True)
    enable_email_verification: bool = Field(default=False)
    
    # Subsystem Configurations
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    email: EmailConfig = Field(default_factory=EmailConfig)
    oauth: OAuthConfig = Field(default_factory=OAuthConfig)
    observability: ObservabilityConfig = Field(default_factory=ObservabilityConfig)
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "validate_assignment": True
    }

    @field_validator('environment')
    @classmethod
    def validate_environment_consistency(cls, v: Environment) -> Environment:
        env_var = os.getenv("ENVIRONMENT", "development").lower()
        if v.value != env_var:
            logger.warning(
                "Environment mismatch between .env and system environment variable",
                config_env=v.value,
                env_var=env_var
            )
        return v

    @field_validator('debug')
    @classmethod
    def validate_debug_in_production(cls, v: bool, info: ValidationInfo) -> bool:
        # 'info.data' is a dict of the other fields
        if 'environment' in info.data and info.data['environment'] == Environment.PRODUCTION and v:
            raise ValueError("Debug mode must be disabled in production")
        return v

    @field_validator('workers')
    @classmethod
    def validate_workers_for_environment(cls, v: int, info: ValidationInfo) -> int:
        if 'environment' in info.data and info.data['environment'] == Environment.PRODUCTION and v < 2:
            logger.warning("Production environment should use at least 2 workers for reliability")
        return v

    # THIS IS THE CORRECT VALIDATOR FOR ApplicationConfig
    @model_validator(mode='after')
    def validate_cross_system_dependencies(self) -> 'ApplicationConfig':
        """Validate dependencies between configuration subsystems."""
        # --- OAuth validation ---
        # If the main `enable_oauth` flag is on, check that the sub-config is valid.
        if self.enable_oauth:
            if not self.oauth:
                raise ValueError("OAuth is enabled, but the [oauth] configuration section is missing.")
            if not self.oauth.google_client_id and not self.oauth.github_client_id:
                logger.warning("OAuth is enabled, but no providers (e.g., Google, GitHub) are configured.")

        # --- Email validation ---
        if self.enable_password_reset:
            if not self.email or not self.email.smtp_username:
                raise ValueError("Email configuration is required because password_reset is enabled.")
        
        # --- Production-specific validations ---
        if self.is_production():
            if self.security and not self.security.enable_rate_limiting:
                logger.warning("Security warning: Rate limiting should be enabled in production.")
        
        return self

    def get_database_url(self) -> str:
        """Get the database URL for the current environment."""
        return self.database.url

    def get_redis_url(self) -> str:
        """Get the Redis URL for the current environment."""
        return self.redis.url

    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == Environment.DEVELOPMENT

    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == Environment.PRODUCTION

    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return self.environment == Environment.TESTING

# Configuration Factory
class ConfigurationManager:
    """
    Configuration manager for centralized configuration access.
    
    FEATURES:
    - Singleton pattern for consistent configuration access
    - Environment-specific configuration loading
    - Runtime configuration updates for feature flags
    - Configuration validation and error reporting
    """
    
    _instance: Optional['ConfigurationManager'] = None
    _config: Optional[ApplicationConfig] = None

    def __new__(cls) -> 'ConfigurationManager':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def load_config(self, config_file: Optional[Path] = None) -> ApplicationConfig:
        """Load and validate application configuration."""
        if self._config is None:
            try:
                # Load from environment variables and .env file
                if config_file and config_file.exists():
                    self._config = ApplicationConfig(_env_file=config_file)
                else:
                    self._config = ApplicationConfig()
                
                logger.info(
                    "Configuration loaded",
                    environment=self._config.environment.value,
                    debug=self._config.debug,
                    app_name=self._config.app_name
                )
                
            except Exception as e:
                logger.error("Configuration loading failed", error=str(e))
                raise
        
        return self._config

    def get_config(self) -> ApplicationConfig:
        """Get the current configuration, loading if necessary."""
        if self._config is None:
            return self.load_config()
        return self._config

    def reload_config(self) -> ApplicationConfig:
        """Reload configuration from environment."""
        self._config = None
        return self.load_config()

    def update_feature_flag(self, flag_name: str, enabled: bool) -> None:
        """Update feature flag at runtime."""
        if self._config and hasattr(self._config, flag_name):
            setattr(self._config, flag_name, enabled)
            logger.info("Feature flag updated", flag=flag_name, enabled=enabled)
        else:
            logger.warning("Unknown feature flag", flag=flag_name)

# Global configuration instance
config_manager = ConfigurationManager()

def get_config() -> ApplicationConfig:
    """Get the application configuration."""
    return config_manager.get_config()

# Environment-specific configuration files support
def load_environment_config(environment: Environment) -> ApplicationConfig:
    """Load configuration for a specific environment."""
    config_file = Path(f".env.{environment.value}")
    return config_manager.load_config(config_file)

# Configuration validation utility
def validate_configuration() -> Dict[str, Any]:
    """
    Validate the current configuration and return a health report.
    
    MONITORING: Configuration health checking for operational readiness
    SECURITY: Security configuration validation
    COMPLIANCE: Ensures configuration meets compliance requirements
    """
    config = get_config()
    health_report = {
        "environment": config.environment.value,
        "validation_timestamp": datetime.datetime.now().isoformat(),
        "issues": [],
        "warnings": [],
        "status": "healthy"
    }
    
    # Security validations
    try:
        jwt_secret_length = len(config.security.jwt_secret_key.get_secret_value())
        if jwt_secret_length < 32:
            health_report["issues"].append("JWT secret key too short")
    except:
        health_report["issues"].append("JWT secret key not configured")
    
    # Database validations
    try:
        db_url = config.get_database_url()
        if "changeme" in db_url:
            health_report["issues"].append("Default database password detected")
    except:
        health_report["issues"].append("Database configuration invalid")
    
    # Production-specific validations
    if config.is_production():
        if config.debug:
            health_report["issues"].append("Debug mode enabled in production")
        
        if not config.security.enable_rate_limiting:
            health_report["warnings"].append("Rate limiting disabled in production")
        
        if config.observability.log_sensitive_data:
            health_report["issues"].append("Sensitive data logging enabled in production")
    
    # Set overall status
    if health_report["issues"]:
        health_report["status"] = "unhealthy"
    elif health_report["warnings"]:
        health_report["status"] = "degraded"
    
    return health_report

# Configuration testing utilities
def create_test_config() -> ApplicationConfig:
    """Create a configuration optimized for testing."""
    return ApplicationConfig(
        environment=Environment.TESTING,
        debug=True,
        database=DatabaseConfig(
            type=DatabaseType.SQLITE,
            name=":memory:"
        ),
        security=SecurityConfig(
            jwt_secret_key=SecretStr("test-jwt-secret-key-minimum-32-characters"),
            max_login_attempts=10,  # Higher for testing
            account_lockout_duration_minutes=1  # Shorter for testing
        ),
        email=EmailConfig(
            smtp_username="<EMAIL>",
            smtp_password=SecretStr("test-password"),
            from_email="<EMAIL>"
        ),
        observability=ObservabilityConfig(
            log_level=LogLevel.DEBUG,
            enable_tracing=False,  # Disabled for testing performance
            enable_metrics=False   # Disabled for testing performance
        )
    )