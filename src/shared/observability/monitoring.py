"""
Enterprise Observability & Health Framework
==========================================
Implements comprehensive monitoring, health checks, and operational readiness
following Gold Standard observability patterns and CCR compliance.

FEATURES:
- Health check endpoints with dependency validation
- Prometheus metrics integration
- Structured logging with correlation tracking
- Performance monitoring with SLI/SLO tracking
- Distributed tracing readiness
- Operational dashboards and alerting
"""

import time
import asyncio
import psutil
import platform
import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
import structlog
from prometheus_client import Counter, Histogram, Gauge, Info, generate_latest, CONTENT_TYPE_LATEST
from fastapi import FastAPI, Request, Response, Depends
from fastapi.responses import JSONResponse, PlainTextResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import httpx
import redis.asyncio as redis

logger = structlog.get_logger()

class HealthStatus(str, Enum):
    """Health check status levels."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"

class ComponentType(str, Enum):
    """Types of system components for health checking."""
    DATABASE = "database"
    CACHE = "cache"
    EMAIL = "email"
    OAUTH = "oauth"
    EXTERNAL_API = "external_api"
    FILESYSTEM = "filesystem"

# Prometheus Metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

ACTIVE_CONNECTIONS = Gauge(
    'active_database_connections',
    'Number of active database connections'
)

CACHE_OPERATIONS = Counter(
    'cache_operations_total',
    'Total cache operations',
    ['operation', 'result']
)

ERROR_COUNT = Counter(
    'application_errors_total',
    'Total application errors',
    ['error_type', 'severity']
)

BUSINESS_METRICS = Counter(
    'business_events_total',
    'Total business events',
    ['event_type']
)

USER_SESSIONS = Gauge(
    'active_user_sessions',
    'Number of active user sessions'
)

SYSTEM_INFO = Info(
    'system_info',
    'System information'
)

@dataclass
class HealthCheckResult:
    """Result of a health check operation."""
    component: str
    status: HealthStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    response_time_ms: float = 0.0
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

@dataclass
class SLIMetrics:
    """Service Level Indicator metrics."""
    availability: float
    latency_p50: float
    latency_p95: float
    latency_p99: float
    error_rate: float
    throughput: float

class HealthChecker:
    """
    Comprehensive health checking system for all application components.
    
    RELIABILITY: Validates all critical dependencies
    OBSERVABILITY: Detailed health status with metrics
    OPERATIONS: Machine-readable health status for orchestration
    """
    
    def __init__(self):
        self.checks: Dict[str, Callable] = {}
        self.last_results: Dict[str, HealthCheckResult] = {}
        self.check_timeout = 10.0  # seconds

    def register_check(
        self,
        name: str,
        check_func: Callable,
        component_type: ComponentType,
        critical: bool = True
    ) -> None:
        """Register a new health check."""
        self.checks[name] = {
            'func': check_func,
            'type': component_type,
            'critical': critical,
            'last_run': None,
            'failure_count': 0
        }
        logger.info("Health check registered", name=name, type=component_type.value)

    async def run_check(self, name: str) -> HealthCheckResult:
        """Run a specific health check with timeout and error handling."""
        check_config = self.checks.get(name)
        if not check_config:
            return HealthCheckResult(
                component=name,
                status=HealthStatus.UNHEALTHY,
                message=f"Unknown health check: {name}"
            )

        start_time = time.time()
        
        try:
            # Run check with timeout
            result = await asyncio.wait_for(
                check_config['func'](),
                timeout=self.check_timeout
            )
            
            response_time = (time.time() - start_time) * 1000
            
            if isinstance(result, HealthCheckResult):
                result.response_time_ms = response_time
                check_config['failure_count'] = 0
                return result
            else:
                # Legacy check function returning boolean
                status = HealthStatus.HEALTHY if result else HealthStatus.UNHEALTHY
                check_config['failure_count'] = 0 if result else check_config['failure_count'] + 1
                
                return HealthCheckResult(
                    component=name,
                    status=status,
                    message="Check completed",
                    response_time_ms=response_time
                )
                
        except asyncio.TimeoutError:
            check_config['failure_count'] += 1
            return HealthCheckResult(
                component=name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check timeout after {self.check_timeout}s",
                response_time_ms=(time.time() - start_time) * 1000
            )
        except Exception as e:
            check_config['failure_count'] += 1
            logger.error("Health check failed", name=name, error=str(e))
            return HealthCheckResult(
                component=name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check error: {str(e)}",
                response_time_ms=(time.time() - start_time) * 1000
            )
        finally:
            check_config['last_run'] = datetime.now(timezone.utc)

    async def run_all_checks(self) -> Dict[str, HealthCheckResult]:
        """Run all registered health checks concurrently."""
        tasks = [
            self.run_check(name) for name in self.checks.keys()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        health_results = {}
        for i, result in enumerate(results):
            check_name = list(self.checks.keys())[i]
            
            if isinstance(result, Exception):
                health_results[check_name] = HealthCheckResult(
                    component=check_name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Check execution failed: {str(result)}"
                )
            else:
                health_results[check_name] = result
        
        self.last_results = health_results
        return health_results

    def get_overall_status(self, results: Dict[str, HealthCheckResult]) -> HealthStatus:
        """Determine overall system health from individual check results."""
        critical_failures = 0
        degraded_count = 0
        
        for name, result in results.items():
            check_config = self.checks[name]
            
            if result.status == HealthStatus.UNHEALTHY and check_config['critical']:
                critical_failures += 1
            elif result.status == HealthStatus.DEGRADED:
                degraded_count += 1
        
        if critical_failures > 0:
            return HealthStatus.UNHEALTHY
        elif degraded_count > 0:
            return HealthStatus.DEGRADED
        else:
            return HealthStatus.HEALTHY

# Specific Health Check Implementations
async def database_health_check(db_session_factory) -> HealthCheckResult:
    """Check database connectivity and performance."""
    try:
        start_time = time.time()
        
        async with db_session_factory() as session:
            # Test basic connectivity
            result = await session.execute(text("SELECT 1"))
            result.fetchone()
            
            # Test transaction capability
            async with session.begin():
                await session.execute(text("SELECT COUNT(*) FROM users"))
        
        response_time = (time.time() - start_time) * 1000
        
        # Update metrics
        ACTIVE_CONNECTIONS.set(5)  # Would be actual connection count in production
        
        if response_time > 1000:  # > 1 second
            return HealthCheckResult(
                component="database",
                status=HealthStatus.DEGRADED,
                message=f"Database responding slowly: {response_time:.1f}ms",
                details={"response_time_ms": response_time}
            )
        
        return HealthCheckResult(
            component="database",
            status=HealthStatus.HEALTHY,
            message="Database connection healthy",
            details={"response_time_ms": response_time}
        )
        
    except Exception as e:
        return HealthCheckResult(
            component="database",
            status=HealthStatus.UNHEALTHY,
            message=f"Database check failed: {str(e)}",
            details={"error": str(e)}
        )

async def redis_health_check(redis_url: str) -> HealthCheckResult:
    """Check Redis cache connectivity and performance."""
    try:
        start_time = time.time()
        
        redis_client = redis.from_url(redis_url)
        
        # Test basic connectivity
        await redis_client.ping()
        
        # Test read/write operations
        test_key = "health_check_test"
        test_value = f"test_{int(time.time())}"
        
        await redis_client.set(test_key, test_value, ex=10)
        retrieved_value = await redis_client.get(test_key)
        await redis_client.delete(test_key)
        
        if retrieved_value.decode() != test_value:
            raise ValueError("Redis read/write test failed")
        
        response_time = (time.time() - start_time) * 1000
        
        await redis_client.close()
        
        return HealthCheckResult(
            component="redis",
            status=HealthStatus.HEALTHY,
            message="Redis cache healthy",
            details={"response_time_ms": response_time}
        )
        
    except Exception as e:
        return HealthCheckResult(
            component="redis",
            status=HealthStatus.UNHEALTHY,
            message=f"Redis check failed: {str(e)}",
            details={"error": str(e)}
        )

async def email_service_health_check(smtp_config: Dict[str, Any]) -> HealthCheckResult:
    """Check email service connectivity."""
    try:
        import aiosmtplib
        
        start_time = time.time()
        
        # Test SMTP connectivity without sending email
        smtp = aiosmtplib.SMTP(
            hostname=smtp_config.get('host', 'smtp.gmail.com'),
            port=smtp_config.get('port', 587),
            use_tls=smtp_config.get('use_tls', True)
        )
        
        await smtp.connect()
        await smtp.login(
            smtp_config['username'],
            smtp_config['password']
        )
        await smtp.quit()
        
        response_time = (time.time() - start_time) * 1000
        
        return HealthCheckResult(
            component="email",
            status=HealthStatus.HEALTHY,
            message="Email service healthy",
            details={"response_time_ms": response_time}
        )
        
    except Exception as e:
        return HealthCheckResult(
            component="email",
            status=HealthStatus.DEGRADED,
            message=f"Email service check failed: {str(e)}",
            details={"error": str(e)}
        )

async def system_resources_health_check() -> HealthCheckResult:
    """Check system resource utilization."""
    try:
        # CPU utilization
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory utilization
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # Disk utilization
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        
        details = {
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
            "disk_percent": disk_percent,
            "available_memory_mb": memory.available // 1024 // 1024
        }
        
        # Determine status based on resource usage
        if cpu_percent > 90 or memory_percent > 90 or disk_percent > 90:
            status = HealthStatus.UNHEALTHY
            message = "Critical resource utilization"
        elif cpu_percent > 75 or memory_percent > 75 or disk_percent > 75:
            status = HealthStatus.DEGRADED
            message = "High resource utilization"
        else:
            status = HealthStatus.HEALTHY
            message = "System resources healthy"
        
        return HealthCheckResult(
            component="system_resources",
            status=status,
            message=message,
            details=details
        )
        
    except Exception as e:
        return HealthCheckResult(
            component="system_resources",
            status=HealthStatus.UNHEALTHY,
            message=f"System check failed: {str(e)}",
            details={"error": str(e)}
        )

class MetricsCollector:
    """
    Comprehensive metrics collection for application monitoring.
    
    OBSERVABILITY: Business and technical metrics collection
    ALERTING: SLI/SLO monitoring for proactive issue detection
    OPTIMIZATION: Performance tracking for continuous improvement
    """
    
    def __init__(self):
        self.request_times = []
        self.error_counts = {}
        self.business_events = {}
        self.start_time = time.time()
        
        # Initialize system info
        SYSTEM_INFO.info({
            'version': '1.0.0',
            'python_version': platform.python_version(),
            'platform': platform.system(),
            'hostname': platform.node()
        })

    def record_request(
        self,
        method: str,
        endpoint: str,
        status_code: int,
        duration: float
    ) -> None:
        """Record HTTP request metrics."""
        REQUEST_COUNT.labels(
            method=method,
            endpoint=endpoint,
            status_code=status_code
        ).inc()
        
        REQUEST_DURATION.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
        
        # Track for SLI calculation
        self.request_times.append(duration)
        if len(self.request_times) > 1000:  # Keep last 1000 requests
            self.request_times = self.request_times[-1000:]

    def record_error(self, error_type: str, severity: str) -> None:
        """Record application error metrics."""
        ERROR_COUNT.labels(
            error_type=error_type,
            severity=severity
        ).inc()
        
        # Track for error rate calculation
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1

    def record_business_event(self, event_type: str) -> None:
        """Record business event metrics."""
        BUSINESS_METRICS.labels(event_type=event_type).inc()
        self.business_events[event_type] = self.business_events.get(event_type, 0) + 1

    def record_cache_operation(self, operation: str, result: str) -> None:
        """Record cache operation metrics."""
        CACHE_OPERATIONS.labels(
            operation=operation,
            result=result
        ).inc()

    def update_active_sessions(self, count: int) -> None:
        """Update active user session count."""
        USER_SESSIONS.set(count)

    def calculate_sli_metrics(self) -> SLIMetrics:
        """Calculate Service Level Indicator metrics."""
        if not self.request_times:
            return SLIMetrics(
                availability=1.0,
                latency_p50=0.0,
                latency_p95=0.0,
                latency_p99=0.0,
                error_rate=0.0,
                throughput=0.0
            )
        
        sorted_times = sorted(self.request_times)
        total_requests = len(sorted_times)
        
        # Calculate percentiles
        p50_index = int(0.50 * total_requests)
        p95_index = int(0.95 * total_requests)
        p99_index = int(0.99 * total_requests)
        
        latency_p50 = sorted_times[p50_index] if p50_index < total_requests else 0
        latency_p95 = sorted_times[p95_index] if p95_index < total_requests else 0
        latency_p99 = sorted_times[p99_index] if p99_index < total_requests else 0
        
        # Calculate error rate
        total_errors = sum(self.error_counts.values())
        error_rate = total_errors / total_requests if total_requests > 0 else 0
        
        # Calculate availability (1 - error rate for 5xx errors)
        server_errors = self.error_counts.get('5xx', 0)
        availability = 1.0 - (server_errors / total_requests) if total_requests > 0 else 1.0
        
        # Calculate throughput (requests per second)
        uptime = time.time() - self.start_time
        throughput = total_requests / uptime if uptime > 0 else 0
        
        return SLIMetrics(
            availability=availability,
            latency_p50=latency_p50,
            latency_p95=latency_p95,
            latency_p99=latency_p99,
            error_rate=error_rate,
            throughput=throughput
        )

# Global instances
health_checker = HealthChecker()
metrics_collector = MetricsCollector()

# FastAPI middleware for automatic metrics collection
class MetricsMiddleware:
    """Middleware for automatic request metrics collection."""
    
    def __init__(self, app: FastAPI):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        start_time = time.time()
        
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                status_code = message["status"]
                duration = time.time() - start_time
                
                # Extract request details
                method = scope["method"]
                path = scope["path"]
                
                # Record metrics
                metrics_collector.record_request(method, path, status_code, duration)
                
                # Log request
                logger.info(
                    "http_request",
                    method=method,
                    path=path,
                    status_code=status_code,
                    duration_ms=duration * 1000
                )
            
            await send(message)

        await self.app(scope, receive, send_wrapper)

# Health check endpoints
async def health_check_endpoint(
    include_details: bool = False,
    db_session: AsyncSession = None
) -> JSONResponse:
    """
    Comprehensive health check endpoint.
    
    OPERATIONS: Machine-readable health status for orchestration
    DEBUGGING: Detailed component status for troubleshooting
    MONITORING: Integration with external monitoring systems
    """
    # Register health checks if not already done
    if "database" not in health_checker.checks and db_session:
        from src.infrastructure.db import get_db
        health_checker.register_check(
            "database",
            lambda: database_health_check(get_db),
            ComponentType.DATABASE,
            critical=True
        )
    
    # Add other checks
    health_checker.register_check(
        "system_resources",
        system_resources_health_check,
        ComponentType.FILESYSTEM,
        critical=False
    )
    
    # Run all health checks
    results = await health_checker.run_all_checks()
    overall_status = health_checker.get_overall_status(results)
    
    # Calculate SLI metrics
    sli_metrics = metrics_collector.calculate_sli_metrics()
    
    response_data = {
        "status": overall_status.value,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": "1.0.0",
        "uptime_seconds": int(time.time() - metrics_collector.start_time),
        "sli_metrics": {
            "availability": sli_metrics.availability,
            "latency_p95_ms": sli_metrics.latency_p95 * 1000,
            "error_rate": sli_metrics.error_rate,
            "throughput_rps": sli_metrics.throughput
        }
    }
    
    if include_details:
        response_data["components"] = {
            name: {
                "status": result.status.value,
                "message": result.message,
                "response_time_ms": result.response_time_ms,
                "timestamp": result.timestamp.isoformat(),
                **result.details
            }
            for name, result in results.items()
        }
    
    # Set appropriate HTTP status code
    http_status = 200 if overall_status == HealthStatus.HEALTHY else 503
    
    return JSONResponse(
        content=response_data,
        status_code=http_status
    )

async def metrics_endpoint() -> PlainTextResponse:
    """Prometheus metrics endpoint."""
    return PlainTextResponse(
        generate_latest(),
        media_type=CONTENT_TYPE_LATEST
    )

async def readiness_check() -> JSONResponse:
    """
    Kubernetes readiness probe endpoint.
    
    KUBERNETES: Simple readiness check for container orchestration
    PERFORMANCE: Lightweight check for high-frequency probing
    """
    # Quick check of critical components only
    try:
        # Test database connectivity with timeout
        from src.infrastructure.db import get_db
        
        db_gen = get_db()
        db_session = await db_gen.__anext__()
        
        try:
            await asyncio.wait_for(
                db_session.execute(text("SELECT 1")),
                timeout=2.0
            )
            return JSONResponse({"status": "ready"})
        finally:
            await db_gen.aclose()
    
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return JSONResponse(
            {"status": "not_ready", "error": str(e)},
            status_code=503
        )

async def liveness_check() -> JSONResponse:
    """
    Kubernetes liveness probe endpoint.
    
    KUBERNETES: Simple liveness check for container orchestration
    RELIABILITY: Basic application health validation
    """
    # Very basic check - just verify the application is responding
    return JSONResponse({
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat()
    })

# Monitoring setup function
def setup_observability(app: FastAPI, config) -> None:
    """
    Set up comprehensive observability for the application.
    
    INTEGRATION: Configures all monitoring components
    OPERATIONS: Production-ready observability stack
    """
    # Add metrics middleware
    app.add_middleware(MetricsMiddleware)
    
    # Add health check endpoints
    app.get("/health")(lambda include_details=False: health_check_endpoint(include_details))
    app.get("/health/detailed")(lambda: health_check_endpoint(include_details=True))
    app.get("/metrics")(metrics_endpoint)
    app.get("/ready")(readiness_check)
    app.get("/alive")(liveness_check)
    
    # Configure structured logging
    structlog.configure(
        processors=[
            structlog.processors.TimeStamper(fmt="ISO"),
            structlog.processors.add_log_level,
            structlog.processors.StackInfoRenderer(),
            structlog.dev.ConsoleRenderer() if config.observability.log_format == "text" 
            else structlog.processors.JSONRenderer()
        ],
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, config.observability.log_level.value)
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    logger.info(
        "Observability configured",
        metrics_enabled=config.observability.enable_metrics,
        tracing_enabled=config.observability.enable_tracing,
        log_level=config.observability.log_level.value
    )

# Usage example for business event tracking
def track_user_registration(user_id: str, registration_method: str) -> None:
    """Track user registration business event."""
    metrics_collector.record_business_event("user_registration")
    logger.info(
        "user_registered",
        user_id=user_id,
        method=registration_method,
        event_type="business_event"
    )

def track_login_attempt(email: str, success: bool, method: str) -> None:
    """Track login attempt with success/failure."""
    event_type = "login_success" if success else "login_failure"
    metrics_collector.record_business_event(event_type)
    
    if not success:
        metrics_collector.record_error("authentication_failed", "medium")
    
    logger.info(
        "login_attempt",
        email=email,
        success=success,
        method=method,
        event_type="business_event"
    )