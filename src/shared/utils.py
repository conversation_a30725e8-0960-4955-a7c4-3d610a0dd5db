from fastapi import Depends, HTTPException, status  # type: ignore[import-not-found]
from fastapi.security import OAuth2<PERSON><PERSON>word<PERSON>earer  # type: ignore[import-not-found]
from jose import JWTError  # type: ignore[import-untyped]
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Any

from src.application.user_service import UserService
from src.infrastructure.db import get_db
from src.shared.security import verify_access_token

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/login")

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_db),
) -> Any:
    try:
        payload = verify_access_token(token)
        user_id = int(payload["sub"])
    except (JW<PERSON>rror, KeyError, ValueError) as err:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
        ) from err
    user_service = UserService()
    user = await user_service.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    return user

def generate_unique_id() -> str:
    """Generate a unique identifier for users."""
    import uuid
    return str(uuid.uuid4())

def format_email(email: str | None) -> str:
    """Format and sanitize the email address. Handles None safely."""
    if not email:
        return ""
    return email.lower().strip()

def is_valid_password(password: str) -> bool:
    """Check if the password meets security requirements."""
    return (
        len(password) >= 12
        and any(char.isdigit() for char in password)
        and any(char.isalpha() for char in password)
    )