"""
Enhanced Main Application - Enterprise Integration
================================================
Integrates all enterprise framework components into a production-ready
FastAPI application with comprehensive observability, security, and performance.

FEATURES:
- Enterprise configuration management with environment-specific settings
- Comprehensive error handling with structured exceptions
- Health checks and metrics endpoints for operational readiness
- Security middleware with rate limiting and CORS
- Structured logging with correlation tracking
- Database initialization with connection pooling
- Graceful shutdown with resource cleanup
"""

import asyncio
import signal
import sys
import datetime
from contextlib import asynccontextmanager
from typing import Dict, Any
import structlog
import uvicorn
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import start_http_server
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

# Import our enterprise framework components
from src.shared.errors.exceptions import (
    EnterpriseException, ValidationError, BusinessRuleViolation,
    IntegrationError, SecurityError, handle_errors,
    CircuitBreaker, CircuitBreakerConfig, RetryConfig
)
from src.config.settings import get_config, validate_configuration
from src.shared.observability.monitoring import (
    metrics_collector, track_user_registration, track_login_attempt, health_checker, ComponentType, setup_observability
)
from src.api.routes import router, register_error_handlers

# Import existing components
from src.infrastructure.db import engine, Base
from src.api.routes import router as original_router

logger = structlog.get_logger()

class ApplicationState:
    """
    Application state management for graceful startup and shutdown.
    
    LIFECYCLE: Manages application lifecycle with proper resource cleanup
    OBSERVABILITY: Tracks application state for monitoring
    RELIABILITY: Ensures graceful handling of shutdown signals
    """
    
    def __init__(self):
        self.is_healthy = False
        self.startup_time = None
        self.shutdown_requested = False
        self.active_connections = 0
        self.background_tasks = set()

    async def startup(self): # <-- REMOVE THE ARGUMENT
        """Application startup sequence with comprehensive initialization."""
        import time
        self.startup_time = time.time()
        
        try:
            config = get_config()
            
            logger.info(
                "application_startup_initiated",
                environment=config.environment.value,
                app_name=config.app_name,
                version=config.app_version
            )
            
            # Initialize database
            await self.initialize_database()
            
            # Validate configuration
            config_health = validate_configuration()
            if config_health["status"] == "unhealthy":
                logger.error(
                    "configuration_validation_failed",
                    issues=config_health["issues"]
                )
                raise RuntimeError("Configuration validation failed")
            
            # Initialize observability
            if config.observability.enable_metrics:
                await self.setup_metrics_server(config)
            
            # Initialize external service health checks
            await self.setup_health_checks(config)
            
            # Initialize Sentry if configured
            if config.observability.sentry_dsn:
                await self.setup_sentry_integration(config)
            
            self.is_healthy = True
            startup_duration = time.time() - self.startup_time
            
            logger.info(
                "application_startup_completed",
                startup_duration=startup_duration,
                health_status="healthy"
            )
            
        except Exception as e:
            logger.error(
                "application_startup_failed",
                error=str(e),
                error_type=type(e).__name__
            )
            self.is_healthy = False
            raise

    async def shutdown(self):
        """Application shutdown sequence with resource cleanup."""
        self.shutdown_requested = True
        
        logger.info(
            "application_shutdown_initiated",
            active_connections=self.active_connections
        )
        
        try:
            # Wait for active connections to complete (with timeout)
            shutdown_timeout = 30  # seconds
            wait_time = 0
            
            while self.active_connections > 0 and wait_time < shutdown_timeout:
                await asyncio.sleep(1)
                wait_time += 1
                
                if wait_time % 5 == 0:  # Log every 5 seconds
                    logger.info(
                        "waiting_for_connections_to_close",
                        active_connections=self.active_connections,
                        wait_time=wait_time
                    )
            
            # Cancel background tasks
            for task in self.background_tasks:
                if not task.done():
                    task.cancel()
            
            if self.background_tasks:
                await asyncio.gather(*self.background_tasks, return_exceptions=True)
            
            # Close database connections
            await engine.dispose()
            
            self.is_healthy = False
            
            logger.info(
                "application_shutdown_completed",
                final_connection_count=self.active_connections
            )
            
        except Exception as e:
            logger.error(
                "application_shutdown_error",
                error=str(e),
                error_type=type(e).__name__
            )

    async def initialize_database(self):
        """Initialize database with proper error handling."""
        try:
            logger.info("initializing_database")
            
            # Create tables
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            
            logger.info("database_initialization_completed")
            
        except Exception as e:
            logger.error(
                "database_initialization_failed",
                error=str(e)
            )
            raise

    async def setup_metrics_server(self, config):
        """Setup Prometheus metrics server."""
        try:
            metrics_port = config.observability.prometheus_port
            start_http_server(metrics_port)
            
            logger.info(
                "metrics_server_started",
                port=metrics_port
            )
            
        except Exception as e:
            logger.warning(
                "metrics_server_setup_failed",
                error=str(e)
            )

    async def setup_health_checks(self, config):
        """Setup health checks for external dependencies."""
        try:
            from src.shared.observability.monitoring import database_health_check, redis_health_check
            from src.infrastructure.db import get_db
            
            # Register database health check
            health_checker.register_check(
                "database",
                lambda: database_health_check(get_db),
                ComponentType.DATABASE,  # <-- use enum, not string
                critical=True
            )
            
            # Register Redis health check if configured
            if hasattr(config, 'redis') and config.redis.host:
                health_checker.register_check(
                    "redis",
                    lambda: redis_health_check(config.redis.url),
                    ComponentType.CACHE,  # <-- use enum, not string
                    critical=False
                )
            
            logger.info("health_checks_configured")
            
        except Exception as e:
            logger.warning(
                "health_checks_setup_failed",
                error=str(e)
            )

    async def setup_sentry_integration(self, config):
        """Setup Sentry error tracking."""
        try:
            sentry_sdk.init(
                dsn=config.observability.sentry_dsn,
                environment=config.environment.value,
                integrations=[
                    FastApiIntegration(auto_enabling_integrations=False),
                    SqlalchemyIntegration()
                ],
                traces_sample_rate=config.observability.trace_sample_rate,
                release=config.app_version
            )
            
            logger.info(
                "sentry_integration_configured",
                environment=config.environment.value
            )
            
        except Exception as e:
            logger.warning(
                "sentry_setup_failed",
                error=str(e)
            )

# Global application state
app_state = ApplicationState()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan management with enterprise-grade startup/shutdown.
    
    RELIABILITY: Proper resource initialization and cleanup
    OBSERVABILITY: Comprehensive lifecycle logging
    ERROR_HANDLING: Graceful error recovery during startup/shutdown
    """
    # Startup
    try:
        await app_state.startup() # <-- CALL WITHOUT ARGUMENTS
        yield
    finally:
        # Shutdown
        await app_state.shutdown()

def create_application() -> FastAPI:
    """
    Create FastAPI application with enterprise configuration.
    
    ARCHITECTURE: Configurable application factory pattern
    SECURITY: Comprehensive security middleware stack
    OBSERVABILITY: Full observability integration
    """
    config = get_config()
    
    # Create FastAPI app with enterprise configuration
    app = FastAPI(
        title=config.app_name,
        version=config.app_version,
        description="Enterprise-grade user login system with comprehensive security and observability",
        docs_url="/docs" if config.is_development() else None,
        redoc_url="/redoc" if config.is_development() else None,
        lifespan=lifespan
    )
    
    # Setup middleware stack
    setup_middleware(app, config)
    
    # Setup observability
    setup_observability(app, config)
    
    # Register routers
    app.include_router(router, prefix="/api/v1", tags=["authentication"])
    
    # Keep original router for backwards compatibility during transition
    if config.is_development():
        app.include_router(original_router, prefix="/legacy", tags=["legacy"])
    
    # Register error handlers
    register_error_handlers(app)
    app.add_exception_handler(EnterpriseException, enterprise_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
    
    # Add custom middleware for request tracking
    @app.middleware("http")
    async def track_requests(request: Request, call_next):
        app_state.active_connections += 1
        
        try:
            response = await call_next(request)
            return response
        finally:
            app_state.active_connections -= 1
    
    return app

def setup_middleware(app: FastAPI, config) -> None:
    """
    Setup comprehensive middleware stack for enterprise security.
    
    SECURITY: CORS, trusted hosts, rate limiting
    MONITORING: Request tracking and metrics collection
    PERFORMANCE: Compression and caching headers
    """
    
    # Trusted Host Middleware (security)
    if config.is_production():
        trusted_hosts = ["*.yourdomain.com", "yourdomain.com"]
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=trusted_hosts
        )
    
    # CORS Middleware
    if config.security.enable_cors:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=config.security.cors_origins or ["*"],
            allow_credentials=config.security.cors_allow_credentials,
            allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            allow_headers=["*"],
        )
    
    # Security Headers Middleware
    @app.middleware("http")
    async def add_security_headers(request: Request, call_next):
        response = await call_next(request)
        
        # Add security headers
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
        }
        
        if config.is_production():
            security_headers.update({
                "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
                "Content-Security-Policy": "default-src 'self'",
            })
        
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response
    
    # Request ID Middleware
    @app.middleware("http")
    async def add_request_id(request: Request, call_next):
        correlation_id = request.headers.get("X-Correlation-ID")
        if not correlation_id:
            import uuid
            correlation_id = f"req_{uuid.uuid4().hex[:12]}"
        
        # Add to request state for use in endpoints
        request.state.correlation_id = correlation_id
        
        response = await call_next(request)
        response.headers["X-Correlation-ID"] = correlation_id
        
        return response

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    
    def signal_handler(signum, frame):
        logger.info(
            "shutdown_signal_received",
            signal=signal.Signals(signum).name
        )
        app_state.shutdown_requested = True
    
    # Register signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    if sys.platform != "win32":
        signal.signal(signal.SIGHUP, signal_handler)

# Create the application instance
app = FastAPI(lifespan=lifespan)

# Use the lifespan context to configure the app at startup
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan management with enterprise-grade startup/shutdown.
    
    RELIABILITY: Proper resource initialization and cleanup
    OBSERVABILITY: Comprehensive lifecycle logging
    ERROR_HANDLING: Graceful error recovery during startup/shutdown
    """
    # Startup
    try:
        await app_state.startup()
        # This will call get_config(), register routers, middleware, etc.
        full_app = create_application()
        # Copy over everything from full_app into our real `app`
        app.router = full_app.router
        app.user_middleware = full_app.user_middleware
        app.dependency_overrides = full_app.dependency_overrides
        app.openapi_schema = full_app.openapi_schema
        yield
    finally:
        # Shutdown
        await app_state.shutdown()

# Root endpoint for basic connectivity testing
@app.get("/")
async def root() -> Dict[str, Any]:
    """Root endpoint with basic application information."""
    config = get_config()
    
    return {
        "message": f"Welcome to {config.app_name}",
        "version": config.app_version,
        "environment": config.environment.value,
        "status": "healthy" if app_state.is_healthy else "starting",
        "docs_url": "/docs" if config.is_development() else None
    }

# Application info endpoint for debugging
@app.get("/info")
async def application_info():
    """Application information for debugging and monitoring."""
    config = get_config()
    
    return {
        "application": {
            "name": config.app_name,
            "version": config.app_version,
            "environment": config.environment.value,
            "debug": config.debug
        },
        "runtime": {
            "startup_time": app_state.startup_time,
            "is_healthy": app_state.is_healthy,
            "active_connections": app_state.active_connections,
            "shutdown_requested": app_state.shutdown_requested
        },
        "features": {
            "registration_enabled": config.enable_registration,
            "oauth_enabled": config.enable_oauth,
            "password_reset_enabled": config.enable_password_reset
        }
    }

def main():
    """
    Main entry point for the application.
    
    DEPLOYMENT: Production-ready with proper configuration
    MONITORING: Comprehensive startup logging
    RELIABILITY: Signal handling and graceful shutdown
    """
    config = get_config()
    
    # Setup signal handlers
    setup_signal_handlers()
    
    # Configure uvicorn for production
    uvicorn_config = {
        "app": "src.main:app",
        "host": config.host,
        "port": config.port,
        "reload": config.reload and config.is_development(),
        "workers": config.workers if not config.reload else 1,
        "access_log": config.observability.log_level.value == "DEBUG",
        "log_config": None,  # We handle logging ourselves
    }
    
    # Add SSL configuration for production
    if config.is_production():
        # SSL configuration would be added here
        # uvicorn_config.update({
        #     "ssl_keyfile": "path/to/keyfile",
        #     "ssl_certfile": "path/to/certfile"
        # })
        pass
    
    logger.info(
        "starting_application",
        **uvicorn_config,
        environment=config.environment.value
    )
    
    # Start the application
    try:
        uvicorn.run(**uvicorn_config)
    except KeyboardInterrupt:
        logger.info("application_interrupted_by_user")
    except Exception as e:
        logger.error(
            "application_startup_failed",
            error=str(e),
            error_type=type(e).__name__
        )
        sys.exit(1)
    finally:
        logger.info("application_shutdown_complete")

if __name__ == "__main__":
    main()

# Export the fully initialized app for testing
application = app