# FILE: src/main.py
# This is the complete, correct file.

import structlog
import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI

# --- Imports from our own enterprise modules ---
from src.api.routes import router as api_router
from src.config.settings import get_config
from src.infrastructure.db import engine, Base
from src.shared.errors.exceptions import (
    enterprise_exception_handler,
    general_exception_handler,
    EnterpriseException,
)
from src.shared.observability.monitoring import (
    setup_observability,
    MetricsMiddleware,
)

logger = structlog.get_logger()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Handles application startup and shutdown events.
    """
    logger.info("Application startup sequence initiated...")
    async with engine.begin() as conn:
        # For development, this creates tables. For production, use Alembic.
        await conn.run_sync(Base.metadata.create_all)
    logger.info("Database tables verified/created.")
    
    yield  # --- The application is now running ---
    
    logger.info("Application shutdown sequence initiated...")
    await engine.dispose()
    logger.info("Database connection pool closed.")

def create_application() -> FastAPI:
    """
    Creates and configures the main FastAPI application object.
    This is our "Application Factory".
    """
    config = get_config()

    app = FastAPI(
        title=config.app_name,
        version=config.app_version,
        lifespan=lifespan,
        docs_url="/docs" if config.is_development() else None,
        redoc_url="/redoc" if config.is_development() else None,
    )

    # Add Middleware (e.g., for metrics, CORS, etc.)
    app.add_middleware(MetricsMiddleware)
    # Note: CORS middleware would be added here if needed.

    # Include the API router. This makes endpoints like /register available.
    app.include_router(api_router, prefix="/api/v1", tags=["User Authentication"])

    # Register our custom enterprise exception handlers.
    app.add_exception_handler(EnterpriseException, enterprise_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)

    # Setup observability (e.g., /health, /metrics endpoints).
    setup_observability(app, config)
    
    @app.get("/", tags=["Root"])
    async def read_root():
        return {"message": f"Welcome to the {config.app_name}"}

    return app

# Create the application instance using our factory.
app = create_application()

# This block allows running the app directly with `python -m src.main`
if __name__ == "__main__":
    config = get_config()
    uvicorn.run(
        "src.main:app",
        host=config.host,
        port=config.port,
        reload=config.reload and config.is_development(),
        log_level=config.observability.log_level.value.lower(),
    )