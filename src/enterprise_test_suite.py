"""
Comprehensive Test Suite - Enterprise Grade
==========================================
Demonstrates the enterprise framework's testing capabilities with multiple
testing strategies: unit, integration, security, performance, and property-based.

FEATURES:
- Complete test isolation with proper AsyncSession handling
- Security vulnerability testing with attack simulation
- Performance benchmarking with SLI validation
- Property-based testing with Hypothesis
- Contract testing for API compliance
- Business logic validation with edge cases
"""

import pytest
import asyncio
import time
import uuid
from typing import Dict, Any, List
from unittest.mock import AsyncMock, patch, MagicMock
from httpx import AsyncClient
from hypothesis import given, strategies as st, settings, Verbosity
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from src.main import app
from src.domain.user import User
from src.application.user_service import UserService
from src.shared.security import hash_password, verify_password, create_access_token

# Import our enhanced components
from .enterprise_error_handling import (
    ValidationError, BusinessRuleViolation, SecurityError, IntegrationError
)
from .enterprise_config import create_test_config
from .observability_framework import metrics_collector

logger = structlog.get_logger()

class TestUserRegistration:
    """
    Comprehensive user registration testing with enterprise patterns.
    
    COVERAGE: Happy path, edge cases, error conditions, security scenarios
    ISOLATION: Each test uses unique data with proper cleanup
    OBSERVABILITY: Performance and business metrics validation
    """

    def generate_unique_email(self) -> str:
        """Generate unique email for test isolation."""
        return f"test_{uuid.uuid4().hex[:8]}@example.com"

    def generate_strong_password(self) -> str:
        """Generate password that meets security requirements."""
        return f"SecurePass123!{uuid.uuid4().hex[:4]}"

    @pytest.mark.asyncio
    async def test_successful_user_registration(
        self,
        async_client: AsyncClient,
        performance_monitor
    ):
        """Test successful user registration with performance monitoring."""
        performance_monitor.start()
        
        email = self.generate_unique_email()
        password = self.generate_strong_password()
        
        response = await async_client.post(
            "/register",
            json={"email": email, "password": password},
            headers={"X-Correlation-ID": "test_reg_001"}
        )
        
        duration, memory_delta = performance_monitor.stop()
        
        # Validate response
        assert response.status_code == 201
        data = response.json()
        assert data["email"] == email
        assert data["is_active"] is True
        assert data["is_google_account"] is False
        assert "id" in data
        
        # Validate performance
        assert duration < 2.0, f"Registration took too long: {duration}s"
        
        logger.info(
            "registration_test_completed",
            email=email,
            duration=duration,
            memory_delta=memory_delta
        )

    @pytest.mark.asyncio
    async def test_duplicate_email_registration(self, async_client: AsyncClient):
        """Test duplicate email registration returns proper error."""
        email = self.generate_unique_email()
        password = self.generate_strong_password()
        
        # First registration
        response1 = await async_client.post(
            "/register",
            json={"email": email, "password": password}
        )
        assert response1.status_code == 201
        
        # Duplicate registration
        response2 = await async_client.post(
            "/register",
            json={"email": email, "password": password}
        )
        assert response2.status_code == 400
        
        data = response2.json()
        assert data["error"]["code"] == "BUSINESS_RULE_EMAIL_ALREADY_EXISTS"
        assert "recovery_actions" in data["error"]
        assert len(data["error"]["recovery_actions"]) > 0

    @pytest.mark.asyncio
    async def test_password_validation_requirements(self, async_client: AsyncClient):
        """Test password validation with various weak passwords."""
        email = self.generate_unique_email()
        
        weak_passwords = [
            "123456",  # Too short
            "password",  # No numbers/symbols
            "Password",  # No numbers/symbols
            "Password123",  # No symbols
            "password123!",  # No uppercase
        ]
        
        for weak_password in weak_passwords:
            response = await async_client.post(
                "/register",
                json={"email": email, "password": weak_password}
            )
            
            # Should fail validation
            assert response.status_code == 422 or response.status_code == 400
            
            if response.status_code == 400:
                data = response.json()
                assert data["error"]["category"] == "validation"

    @pytest.mark.asyncio
    async def test_email_validation(self, async_client: AsyncClient):
        """Test email validation with invalid formats."""
        password = self.generate_strong_password()
        
        invalid_emails = [
            "notanemail",
            "@example.com",
            "user@",
            "user@.com",
            "<EMAIL>",
            ""
        ]
        
        for invalid_email in invalid_emails:
            response = await async_client.post(
                "/register",
                json={"email": invalid_email, "password": password}
            )
            
            assert response.status_code == 422
            data = response.json()
            assert "email" in str(data).lower()

    @pytest.mark.asyncio
    async def test_registration_with_correlation_tracking(self, async_client: AsyncClient):
        """Test correlation ID tracking through registration flow."""
        correlation_id = f"test_{uuid.uuid4().hex[:8]}"
        email = self.generate_unique_email()
        password = self.generate_strong_password()
        
        response = await async_client.post(
            "/register",
            json={"email": email, "password": password},
            headers={"X-Correlation-ID": correlation_id}
        )
        
        assert response.status_code == 201
        # Correlation ID should be preserved in logs and can be validated through observability


class TestUserAuthentication:
    """
    Comprehensive authentication testing with security focus.
    
    SECURITY: Attack simulation, rate limiting, account lockout
    PERFORMANCE: Login performance benchmarking
    BUSINESS_LOGIC: Authentication flow validation
    """

    @pytest.mark.asyncio
    async def test_successful_login_flow(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession
    ):
        """Test complete login flow with token validation."""
        # Create user first
        email = f"login_test_{uuid.uuid4().hex[:8]}@example.com"
        password = f"LoginPass123!{uuid.uuid4().hex[:4]}"
        
        user_service = UserService()
        user = await user_service.register_user(
            db=db_session,
            email=email,
            password=password
        )
        
        # Test login
        response = await async_client.post(
            "/login",
            json={"email": email, "password": password}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        
        # Test token usage
        token = data["access_token"]
        me_response = await async_client.get(
            "/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert me_response.status_code == 200
        me_data = me_response.json()
        assert me_data["email"] == email
        assert me_data["id"] == user.id

    @pytest.mark.asyncio
    async def test_invalid_credentials(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test login with invalid credentials."""
        # Create user
        email = f"invalid_test_{uuid.uuid4().hex[:8]}@example.com"
        password = f"ValidPass123!{uuid.uuid4().hex[:4]}"
        
        user_service = UserService()
        await user_service.register_user(
            db=db_session,
            email=email,
            password=password
        )
        
        # Test with wrong password
        response = await async_client.post(
            "/login",
            json={"email": email, "password": "WrongPassword123!"}
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data["error"]["code"] == "SECURITY_AUTHENTICATION_FAILED"

    @pytest.mark.asyncio
    async def test_nonexistent_user_login(self, async_client: AsyncClient):
        """Test login with non-existent user."""
        response = await async_client.post(
            "/login",
            json={
                "email": f"nonexistent_{uuid.uuid4().hex[:8]}@example.com",
                "password": "SomePassword123!"
            }
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data["error"]["code"] == "SECURITY_AUTHENTICATION_FAILED"

    @pytest.mark.asyncio
    async def test_token_expiration_handling(self, async_client: AsyncClient):
        """Test handling of expired tokens."""
        # Create an expired token
        expired_token = create_access_token(
            data={"sub": "999"},
            expires_delta=timedelta(seconds=-1)  # Already expired
        )
        
        response = await async_client.get(
            "/me",
            headers={"Authorization": f"Bearer {expired_token}"}
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data["error"]["code"] == "SECURITY_TOKEN_INVALID"


class TestSecurityVulnerabilities:
    """
    Security vulnerability testing with attack simulation.
    
    SECURITY: SQL injection, XSS, CSRF, authentication bypass attempts
    COMPLIANCE: OWASP ASVS testing scenarios
    MONITORING: Security event logging validation
    """

    @pytest.mark.asyncio
    async def test_sql_injection_protection(
        self,
        async_client: AsyncClient,
        security_test_utils
    ):
        """Test SQL injection protection in login endpoint."""
        sql_payloads = security_test_utils.sql_injection_payloads()
        
        for payload in sql_payloads:
            response = await async_client.post(
                "/login",
                json={"email": payload, "password": "password"}
            )
            
            # Should not cause server error (500)
            assert response.status_code != 500
            
            # Should return proper authentication error
            if response.status_code == 401:
                data = response.json()
                assert "error" in data
                # Should not expose internal database errors
                assert "sql" not in data["error"]["message"].lower()
                assert "database" not in data["error"]["message"].lower()

    @pytest.mark.asyncio
    async def test_xss_protection_in_responses(
        self,
        async_client: AsyncClient,
        security_test_utils
    ):
        """Test XSS protection in API responses."""
        xss_payloads = security_test_utils.xss_payloads()
        
        for payload in xss_payloads:
            # Try to register with XSS payload in email (will fail validation)
            response = await async_client.post(
                "/register",
                json={"email": payload, "password": "SecurePass123!"}
            )
            
            # Ensure response doesn't contain unescaped payload
            response_text = response.text
            assert "<script>" not in response_text
            assert "javascript:" not in response_text
            assert "onerror=" not in response_text

    @pytest.mark.asyncio
    async def test_password_brute_force_protection(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession
    ):
        """Test protection against password brute force attacks."""
        # Create user
        email = f"brute_force_test_{uuid.uuid4().hex[:8]}@example.com"
        password = f"SecurePass123!{uuid.uuid4().hex[:4]}"
        
        user_service = UserService()
        await user_service.register_user(
            db=db_session,
            email=email,
            password=password
        )
        
        # Attempt multiple failed logins
        failed_attempts = 0
        for i in range(10):  # Try 10 failed logins
            response = await async_client.post(
                "/login",
                json={"email": email, "password": f"wrong_password_{i}"}
            )
            
            if response.status_code == 401:
                failed_attempts += 1
            elif response.status_code == 429:  # Rate limited
                break  # This is expected behavior
        
        # Should have some rate limiting or account lockout mechanism
        # (Specific implementation depends on configuration)
        assert failed_attempts > 0  # At least some attempts should fail normally

    @pytest.mark.asyncio
    async def test_authentication_bypass_attempts(self, async_client: AsyncClient):
        """Test various authentication bypass attempts."""
        bypass_attempts = [
            {"Authorization": "Bearer fake_token"},
            {"Authorization": "Bearer "},
            {"Authorization": "Basic fake"},
            {"Authorization": ""},
        ]
        
        for headers in bypass_attempts:
            response = await async_client.get("/me", headers=headers)
            
            # Should always require proper authentication
            assert response.status_code == 401
            data = response.json()
            assert "error" in data


class TestPerformanceAndReliability:
    """
    Performance and reliability testing with SLI validation.
    
    PERFORMANCE: Response time, throughput, resource usage
    RELIABILITY: Concurrent operations, error recovery
    SCALABILITY: Load testing and resource optimization
    """

    @pytest.mark.asyncio
    async def test_registration_performance_benchmark(
        self,
        async_client: AsyncClient,
        performance_monitor
    ):
        """Benchmark registration performance under normal load."""
        performance_monitor.start()
        
        # Perform multiple registrations concurrently
        tasks = []
        for i in range(10):
            email = f"perf_test_{i}_{uuid.uuid4().hex[:6]}@example.com"
            password = f"PerfTest123!{i}"
            
            task = async_client.post(
                "/register",
                json={"email": email, "password": password}
            )
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks)
        duration, memory_delta = performance_monitor.stop()
        
        # Validate all registrations succeeded
        for response in responses:
            assert response.status_code == 201
        
        # Validate performance SLIs
        avg_response_time = duration / len(tasks)
        assert avg_response_time < 1.0, f"Average response time too high: {avg_response_time}s"
        assert duration < 5.0, f"Total time too high: {duration}s"
        
        logger.info(
            "performance_benchmark_completed",
            total_duration=duration,
            avg_response_time=avg_response_time,
            memory_delta=memory_delta,
            concurrent_operations=len(tasks)
        )

    @pytest.mark.asyncio
    async def test_database_connection_handling(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession
    ):
        """Test database connection handling under concurrent load."""
        # Create multiple concurrent database operations
        user_service = UserService()
        
        tasks = []
        for i in range(20):  # 20 concurrent operations
            email = f"db_test_{i}_{uuid.uuid4().hex[:6]}@example.com"
            password = f"DbTest123!{i}"
            
            task = user_service.register_user(
                db=db_session,
                email=email,
                password=password
            )
            tasks.append(task)
        
        # Execute all operations concurrently
        start_time = time.time()
        users = await asyncio.gather(*tasks, return_exceptions=True)
        duration = time.time() - start_time
        
        # Validate results
        successful_operations = 0
        for user in users:
            if isinstance(user, User):
                successful_operations += 1
            else:
                logger.warning("Database operation failed", error=str(user))
        
        # Should handle concurrent operations gracefully
        assert successful_operations > 15, f"Too many failed operations: {successful_operations}/20"
        assert duration < 10.0, f"Operations took too long: {duration}s"

    @pytest.mark.asyncio 
    async def test_memory_usage_under_load(
        self,
        async_client: AsyncClient,
        performance_monitor
    ):
        """Test memory usage under sustained load."""
        import psutil
        
        initial_memory = psutil.Process().memory_info().rss
        performance_monitor.start()
        
        # Perform sustained operations
        for batch in range(5):  # 5 batches
            tasks = []
            for i in range(10):  # 10 operations per batch
                email = f"memory_test_{batch}_{i}_{uuid.uuid4().hex[:4]}@example.com"
                password = f"MemTest123!{batch}{i}"
                
                task = async_client.post(
                    "/register",
                    json={"email": email, "password": password}
                )
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks)
            
            # Validate batch success
            for response in responses:
                assert response.status_code == 201
            
            # Brief pause between batches
            await asyncio.sleep(0.1)
        
        duration, memory_delta = performance_monitor.stop()
        final_memory = psutil.Process().memory_info().rss
        total_memory_growth = final_memory - initial_memory
        
        # Memory growth should be reasonable
        max_acceptable_growth = 100 * 1024 * 1024  # 100MB
        assert total_memory_growth < max_acceptable_growth, \
            f"Excessive memory growth: {total_memory_growth / 1024 / 1024:.2f}MB"


class TestPropertyBasedScenarios:
    """
    Property-based testing using Hypothesis for edge case discovery.
    
    RELIABILITY: Automatic edge case generation
    COVERAGE: Exhaustive input validation testing
    ROBUSTNESS: Fault tolerance validation
    """

    @given(
        email=st.emails(),
        password=st.text(min_size=12, max_size=128)
    )
    @settings(max_examples=50, verbosity=Verbosity.verbose)
    @pytest.mark.asyncio
    async def test_user_registration_with_generated_inputs(
        self,
        async_client: AsyncClient,
        email: str,
        password: str
    ):
        """Test user registration with property-based generated inputs."""
        # Filter out emails that would cause issues with our test database
        if len(email) > 254 or "@example.com" in email:
            return  # Skip problematic test cases
        
        response = await async_client.post(
            "/register",
            json={"email": email, "password": password}
        )
        
        # Response should always be well-formed
        assert response.status_code in [201, 400, 422]
        
        if response.status_code != 201:
            # Validation failures should have proper error structure
            data = response.json()
            assert "error" in data or "detail" in data

    @given(
        password=st.text(min_size=1, max_size=50).filter(
            lambda x: not any(c in x for c in ['"', "'", ";", "--"])
        )
    )
    @settings(max_examples=30)
    @pytest.mark.asyncio
    async def test_password_hashing_properties(self, password: str):
        """Test password hashing properties with generated passwords."""
        if len(password) < 3:  # Skip very short passwords
            return
        
        # Test password hashing consistency
        hash1 = hash_password(password)
        hash2 = hash_password(password)
        
        # Hashes should be different (due to salt)
        assert hash1 != hash2
        
        # But both should verify correctly
        assert verify_password(password, hash1)
        assert verify_password(password, hash2)
        
        # Wrong password should not verify
        assert not verify_password(password + "wrong", hash1)


class TestBusinessLogicValidation:
    """
    Business logic validation with comprehensive edge case testing.
    
    BUSINESS_RULES: Domain logic validation
    EDGE_CASES: Boundary condition testing
    INTEGRATION: Cross-component interaction testing
    """

    @pytest.mark.asyncio
    async def test_user_lifecycle_complete_flow(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        mock_email_service
    ):
        """Test complete user lifecycle from registration to password reset."""
        email = f"lifecycle_test_{uuid.uuid4().hex[:8]}@example.com"
        original_password = f"OriginalPass123!{uuid.uuid4().hex[:4]}"
        new_password = f"NewPass123!{uuid.uuid4().hex[:4]}"
        
        # 1. Register user
        register_response = await async_client.post(
            "/register",
            json={"email": email, "password": original_password}
        )
        assert register_response.status_code == 201
        user_data = register_response.json()
        
        # 2. Login with original password
        login_response = await async_client.post(
            "/login",
            json={"email": email, "password": original_password}
        )
        assert login_response.status_code == 200
        token_data = login_response.json()
        
        # 3. Access protected endpoint
        me_response = await async_client.get(
            "/me",
            headers={"Authorization": f"Bearer {token_data['access_token']}"}
        )
        assert me_response.status_code == 200
        
        # 4. Request password reset
        reset_request_response = await async_client.post(
            "/password-reset/request",
            json={"email": email}
        )
        assert reset_request_response.status_code == 202
        
        # Verify email was sent
        mock_email_service.assert_called_once()
        
        # 5. Simulate password reset (would require token from email in real flow)
        # For testing, we'll create a valid reset token
        reset_token = create_access_token(
            data={"sub": str(user_data["id"]), "type": "password_reset"},
            expires_delta=timedelta(hours=1)
        )
        
        reset_confirm_response = await async_client.post(
            "/password-reset/confirm",
            json={"token": reset_token, "new_password": new_password}
        )
        assert reset_confirm_response.status_code == 200
        
        # 6. Login with new password should work
        new_login_response = await async_client.post(
            "/login",
            json={"email": email, "password": new_password}
        )
        assert new_login_response.status_code == 200
        
        # 7. Login with old password should fail
        old_login_response = await async_client.post(
            "/login",
            json={"email": email, "password": original_password}
        )
        assert old_login_response.status_code == 401

    @pytest.mark.asyncio
    async def test_edge_case_email_formats(self, async_client: AsyncClient):
        """Test edge cases in email format validation."""
        password = "EdgeCaseTest123!"
        
        edge_case_emails = [
            # Valid edge cases
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            
            # Invalid edge cases that should be rejected
            "plainaddress",
            "@domain.com",
            "user@",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in edge_case_emails:
            response = await async_client.post(
                "/register",
                json={"email": email, "password": password}
            )
            
            # Log the result for analysis
            logger.info(
                "edge_case_email_test",
                email=email,
                status_code=response.status_code,
                is_valid_format="@" in email and "." in email.split("@")[-1]
            )
            
            # Should either succeed (201) or fail validation (422/400)
            assert response.status_code in [201, 400, 422]


# Test configuration and fixtures for the comprehensive suite
@pytest.fixture
def comprehensive_test_config():
    """Configuration optimized for comprehensive testing."""
    config = create_test_config()
    
    # Adjust for testing performance
    config.security.max_login_attempts = 10  # Higher for testing
    config.security.account_lockout_duration_minutes = 1  # Shorter for testing
    
    return config


# Performance test markers
pytestmark = [
    pytest.mark.asyncio,
    pytest.mark.timeout(30),  # 30 second timeout for all tests
]

# Test execution summary
class TestSuiteMetrics:
    """Track test suite execution metrics."""
    
    @pytest.fixture(autouse=True)
    def track_test_execution(self, request):
        """Automatically track test execution metrics."""
        start_time = time.time()
        yield
        duration = time.time() - start_time
        
        logger.info(
            "test_execution_completed",
            test_name=request.node.name,
            duration=duration,
            test_class=request.cls.__name__ if request.cls else None
        )