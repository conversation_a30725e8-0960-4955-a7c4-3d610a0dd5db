"""
User domain model and validation logic.
Follows Gold Standard Guidelines and CCR for type safety and validation.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String
from sqlalchemy.sql import func

from src.infrastructure.db import Base


class User(Base):  # type: ignore[misc,valid-type]
    __tablename__ = "users"
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_google_account = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return f"<User(email={self.email}, active={self.is_active})>"