import os
from datetime import datetime, timedelta, timezone
from typing import Any, Optional

from jose import jwt, JW<PERSON>rror
from passlib.context import CryptContext  # type: ignore[import-untyped]
from pydantic import BaseModel, EmailStr  # type: ignore[import-not-found]

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT configuration
JWT_SECRET = os.getenv("JWT_SECRET_KEY", "your_jwt_secret")
JWT_ALGORITHM = "HS256"
JWT_EXP_DELTA_SECONDS = 3600  # 1 hour

class User(BaseModel):  # type: ignore[misc]
    id: int
    email: EmailStr
    hashed_password: str
    is_active: bool = True

def hash_password(password: str) -> str:
    """Hash a password for storing."""
    return pwd_context.hash(password)  # type: ignore[no-any-return]

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a stored password against one provided by user."""
    return pwd_context.verify(plain_password, hashed_password)  # type: ignore[no-any-return]

def create_access_token(data: dict[str, Any], expires_delta: timedelta | None = None) -> str:
    """Create a JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(seconds=JWT_EXP_DELTA_SECONDS)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, JWT_SECRET, algorithm=JWT_ALGORITHM)  # type: ignore[no-any-return]

def decode_access_token(token: str) -> Optional[dict[str, Any]]:
    """Decode a JWT access token."""
    try:
        return jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])  # type: ignore[no-any-return]
    except JWTError:
        return None