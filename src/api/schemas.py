import os

from pydantic import BaseModel, EmailStr, SecretStr  # type: ignore[import-not-found]

"""
Pydantic schemas for API requests and responses.
Follows Gold Standard Guidelines and CCR for input validation and documentation.
"""


class UserCreate(BaseModel):  # type: ignore[misc]
    email: EmailStr
    password: SecretStr


class UserLogin(BaseModel):  # type: ignore[misc]
    email: EmailStr
    password: SecretStr


class UserResponse(BaseModel):  # type: ignore[misc]
    id: int
    email: str  # Use str for compatibility with ORM and mypy
    is_active: bool
    is_google_account: bool


class Token(BaseModel):  # type: ignore[misc]
    access_token: str
    token_type: str = os.getenv("TOKEN_TYPE", "bearer")


class PasswordResetRequest(BaseModel):  # type: ignore[misc]
    email: EmailStr


class PasswordResetConfirm(BaseModel):  # type: ignore[misc]
    token: str
    new_password: SecretStr