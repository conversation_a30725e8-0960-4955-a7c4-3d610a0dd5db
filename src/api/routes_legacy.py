import os
from typing import Any, Annotated

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.api.schemas import (
    Token,
    UserCreate,
    UserLogin,
    UserResponse,
)
from src.application.auth_service import AuthService
from src.application.user_service import UserService
from src.infrastructure.db import get_db
from src.shared.security import create_access_token, hash_password
from src.shared.utils import get_current_user

router = APIRouter()


def get_db_session() -> Any:
    return Depends(get_db)


def get_current_user_dep() -> Any:
    # Deprecated: use Depends(get_current_user) directly in route signatures
    return Depends(get_current_user)


@router.post(
    "/register",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
)
async def register(user: UserCreate, db: Annotated[AsyncSession, Depends(get_db)]):
    user_service = UserService()
    try:
        new_user = await user_service.register_user(
            db,
            user.email,
            user.password.get_secret_value(),
        )
        return UserResponse(
            id=getattr(new_user, "id"),
            email=getattr(new_user, "email"),
            is_active=getattr(new_user, "is_active"),
            is_google_account=getattr(new_user, "is_google_account"),
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e


@router.post("/login", response_model=Token)
async def login(user: UserLogin, db: Annotated[AsyncSession, Depends(get_db)]):
    auth_service = AuthService()
    try:
        token = await auth_service.login(
            db,
            user.email,
            user.password.get_secret_value(),
        )
        return Token(access_token=token)
    except ValueError as e:
        raise HTTPException(status_code=401, detail=str(e)) from e


@router.get("/me", response_model=UserResponse)
async def read_users_me(current_user: Any = Depends(get_current_user)) -> UserResponse:
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        is_active=current_user.is_active,
        is_google_account=current_user.is_google_account,
    )


# Password reset and Google OAuth endpoints are removed for MVP simplicity.