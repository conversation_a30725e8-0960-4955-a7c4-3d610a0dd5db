"""
Email service for sending password reset and notification emails via Gmail.
Follows Gold Standard Guidelines and CCR for security and observability.
"""
import os
from email.message import EmailMessage

import aiosmtplib  # type: ignore[import-not-found]
import structlog  # type: ignore[import-not-found]

logger = structlog.get_logger()

GMAIL_USER = os.getenv("GMAIL_USER")
GMAIL_PASS = os.getenv("GMAIL_PASS")

if not GMAIL_USER or not GMAIL_PASS:
    logger.warning("Gmail credentials not set in environment variables.")

async def send_email_async(subject: str, recipient: str, body: str) -> None:
    """Send an email asynchronously using Gmail SMTP."""
    if not GMAIL_USER:
        logger.error("GMAIL_USER environment variable is not set. Cannot send email.")
        raise RuntimeError("GMAIL_USER environment variable is not set. Please configure a valid sender email address.")
    message = EmailMessage()
    message["From"] = GMAIL_USER
    message["To"] = recipient
    message["Subject"] = subject
    message.set_content(body)
    try:
        await aiosmtplib.send(
            message,
            hostname="smtp.gmail.com",
            port=587,
            start_tls=True,
            username=GMAIL_USER,
            password=GMAIL_PASS,
        )
        logger.info("Email sent", to=recipient, subject=subject)
    except Exception as e:
        logger.error("Failed to send email", error=str(e), to=recipient)
        raise