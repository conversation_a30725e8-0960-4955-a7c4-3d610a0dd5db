[project]
root = user-login-system

[folders]
src = src/
domain = src/domain/
application = src/application/
infrastructure = src/infrastructure/
api = src/api/
shared = src/shared/
tests = tests/

[files]
readme = README.md
prd = PRD.md
gold_standard = ../Gold_Standard_Guidelines.md
critical_rules = ../critical_coding_rules.md
pyproject = pyproject.toml
env_example = .env.example

[modules]
domain_user = src/domain/user.py
domain_auth = src/domain/auth.py
application_user_service = src/application/user_service.py
application_auth_service = src/application/auth_service.py
infrastructure_db = src/infrastructure/db.py
infrastructure_email_service = src/infrastructure/email_service.py
api_routes = src/api/routes.py  # Legacy/minimal API endpoints (MVP, reference only)
api_schemas = src/api/schemas.py
shared_security = src/shared/security.py
shared_utils = src/shared/utils.py
tests_user = tests/test_user.py
tests_auth = tests/test_auth.py
tests_api = tests/test_api.py
tests_conftest = tests/conftest.py
enterprise_test_suite = src/enterprise_test_suite.py
enterprise_observability = src/enterprise_observability.py
enhanced_routes = src/enhanced_routes.py  # Main production API layer (enterprise features)
enterprise_error_handling = src/enterprise_error_handling.py
enterprise_config = src/enterprise_config.py

# ===============================
#  User Login System File Map
# ===============================
# LEGEND:
#   ✅ = Complete & in repo
#   🟡 = Exists, but needs work
#   ⬜ = To be created
#
# ┌─ user-login-system/
# │
# ├── .env.example                ✅  # Example environment config
# ├── .coverage                   ✅  # Test coverage data
# ├── poetry.lock                 ✅  # Poetry lockfile
# ├── pyproject.toml              ✅  # Project config & dependencies
# ├── README.md                   ✅  # Project overview & workflow
# ├── PRD.md                      ✅  # Product requirements & CTO report
# ├── filestructure.ini           ✅  # This file (annotated)
# ├── user_login.db               ✅  # SQLite DB (dev/test)
# │
# ├─ src/                         ✅  # Main application code
# │   ├─ __init__.py              ✅
# │   ├─ main.py                  ✅  # App entrypoint (could add health checks ⬜)
# │   ├─ api/                     ✅
# │   │   ├─ __init__.py          ✅
# │   │   ├─ routes.py            ✅  # FastAPI endpoints
# │   │   ├─ schemas.py           ✅  # Pydantic models
# │   ├─ application/             ✅
# │   │   ├─ __init__.py          ✅
# │   │   ├─ user_service.py      ✅  # User business logic
# │   │   ├─ auth_service.py      ✅  # Auth logic
# │   ├─ domain/                  ✅
# │   │   ├─ __init__.py          ✅
# │   │   ├─ user.py              ✅  # User ORM model
# │   │   ├─ auth.py              ✅  # Auth domain logic
# │   ├─ infrastructure/          ✅
# │   │   ├─ __init__.py          ✅
# │   │   ├─ db.py                ✅  # DB connection/session
# │   │   ├─ email_service.py     ✅  # Email sending
# │   ├─ shared/                  ✅
# │   │   ├─ __init__.py          ✅
# │   │   ├─ security.py          ✅  # Security utils
# │   │   ├─ utils.py             ✅  # Misc helpers
# │
# ├─ tests/                       ✅  # Test suite
# │   ├─ __pycache__/             ✅
# │   ├─ conftest.py              ✅  # Fixtures (DB, client, etc.)
# │   ├─ test_api.py              ✅  # API endpoint tests
# │   ├─ test_auth.py             ✅  # Auth logic tests
# │   ├─ test_user.py             ✅  # User logic tests
# │   ├─ .env.test                ✅  # Test env config
# │
# ├─ Gold_Standard_Guidelines.md  ✅  # Coding standards
# ├─ critical_coding_rules.md     ✅  # CCR rules
#
# ========== TO BE CREATED / IMPROVED ========== #
#
# src/health.py                   ⬜  # Health check endpoint (operational readiness)
# src/observability.py            ⬜  # Metrics/tracing (see Gold Standard)
# src/config.py                   ⬜  # Centralized config (pydantic BaseSettings)
# src/domain/errors.py            ⬜  # Exception hierarchy (see Gold Standard)
# src/domain/repository.py        ⬜  # Repository protocol (for DI/testing)
#
# The following are implemented in enterprise_* files:
# - Health checks, observability, and metrics: see enterprise_observability.py
# - Centralized config: see enterprise_config.py
# - Exception hierarchy and error handling: see enterprise_error_handling.py
# - Property-based and performance tests: see enterprise_test_suite.py
#
# Only add the above as separate files if you want to split or alias functionality.
#
# docs/                           ⬜  # API docs, ADRs, deployment guides
#
# (Review Gold_Standard_Guidelines.md for further suggestions)
#
# Enterprise cross-cutting modules (all in src/):
#   enterprise_observability.py      ✅  # Health checks, metrics, tracing, operational readiness
#   enterprise_config.py             ✅  # Centralized config management (Pydantic, env, secrets)
#   enterprise_error_handling.py     ✅  # Exception hierarchy, error handling, circuit breakers
#   enterprise_test_suite.py         ✅  # Comprehensive test suite (unit, integration, property, perf)
