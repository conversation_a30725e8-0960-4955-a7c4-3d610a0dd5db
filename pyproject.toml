[build-system]
requires = ["setuptools>=64", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "enterprise-user-login-system"
version = "1.0.0"
description = "Enterprise-grade user login system with comprehensive security, observability, and performance optimization"
authors = [
    {name = "Enterprise Development Team", email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Security",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]
keywords = ["fastapi", "authentication", "security", "enterprise", "oauth", "jwt"]

# Core Dependencies - Production Requirements
dependencies = [
    # Web Framework
    "fastapi>=0.104.0,<1.0.0",
    "uvicorn[standard]>=0.24.0,<1.0.0",
    
    # Data Validation and Serialization
    "pydantic[email]>=2.5.0,<3.0.0",
    "pydantic-settings>=2.0.0,<3.0.0",
    
    # Database and ORM
    "sqlalchemy[asyncio]>=2.0.0,<3.0.0",
    "alembic>=1.13.0,<2.0.0",
    "aiosqlite>=0.19.0,<1.0.0",  # SQLite for development/testing
    "asyncpg>=0.29.0,<1.0.0",   # PostgreSQL for production
    
    # Authentication and Security
    "passlib[bcrypt]>=1.7.4,<2.0.0",
    "python-jose[cryptography]>=3.3.0,<4.0.0",
    "python-multipart>=0.0.6,<1.0.0",
    "cryptography>=41.0.0,<42.0.0",
    
    # HTTP Client
    "httpx>=0.25.0,<1.0.0",
    "authlib>=1.2.0,<2.0.0",  # OAuth integrations
    
    # Email
    "aiosmtplib>=3.0.0,<4.0.0",
    "email-validator>=2.1.0,<3.0.0",
    
    # Environment and Configuration
    "python-dotenv>=1.0.0,<2.0.0",
    
    # Logging and Observability
    "structlog>=23.2.0,<24.0.0",
    "prometheus-client>=0.19.0,<1.0.0",
    
    # Caching and Session Management
    "redis>=5.0.0,<6.0.0",
    
    # System Monitoring
    "psutil>=5.9.0,<6.0.0",
    
    # Error Tracking (optional)
    "sentry-sdk[fastapi]>=1.38.0,<2.0.0",
]

[project.optional-dependencies]
# Development Dependencies
dev = [
    # Testing Framework
    "pytest>=7.4.0,<8.0.0",
    "pytest-asyncio>=0.21.0,<1.0.0",
    "pytest-cov>=4.1.0,<5.0.0",
    "pytest-mock>=3.12.0,<4.0.0",
    "pytest-timeout>=2.2.0,<3.0.0",
    "pytest-xdist>=3.5.0,<4.0.0",  # Parallel test execution
    
    # Property-based Testing
    "hypothesis>=6.88.0,<7.0.0",
    
    # Integration Testing
    "testcontainers>=3.7.0,<4.0.0",
    "httpx>=0.25.0,<1.0.0",
    
    # Load Testing
    "locust>=2.17.0,<3.0.0",
    
    # Code Quality and Static Analysis
    "mypy>=1.7.0,<2.0.0",
    "ruff>=0.1.0,<1.0.0",
    "black>=23.10.0,<24.0.0",
    "isort>=5.12.0,<6.0.0",
    "pre-commit>=3.5.0,<4.0.0",
    
    # Security Scanning
    "bandit[toml]>=1.7.5,<2.0.0",
    "safety>=2.3.0,<3.0.0",
    "pip-audit>=2.6.0,<3.0.0",
    "semgrep>=1.45.0,<2.0.0",
    
    # Documentation
    "mkdocs>=1.5.0,<2.0.0",
    "mkdocs-material>=9.4.0,<10.0.0",
    "mkdocstrings[python]>=0.24.0,<1.0.0",
    
    # Database Migrations (dev)
    "alembic>=1.13.0,<2.0.0",
]

# Production Dependencies (subset for minimal production images)
production = [
    "fastapi>=0.104.0,<1.0.0",
    "uvicorn[standard]>=0.24.0,<1.0.0",
    "pydantic[email]>=2.5.0,<3.0.0",
    "pydantic-settings>=2.0.0,<3.0.0",
    "sqlalchemy[asyncio]>=2.0.0,<3.0.0",
    "asyncpg>=0.29.0,<1.0.0",  # PostgreSQL only for production
    "passlib[bcrypt]>=1.7.4,<2.0.0",
    "python-jose[cryptography]>=3.3.0,<4.0.0",
    "cryptography>=41.0.0,<42.0.0",
    "httpx>=0.25.0,<1.0.0",
    "aiosmtplib>=3.0.0,<4.0.0",
    "python-dotenv>=1.0.0,<2.0.0",
    "structlog>=23.2.0,<24.0.0",
    "prometheus-client>=0.19.0,<1.0.0",
    "redis>=5.0.0,<6.0.0",
    "psutil>=5.9.0,<6.0.0",
    "sentry-sdk[fastapi]>=1.38.0,<2.0.0",
]

# Security-focused dependencies for security auditing
security = [
    "bandit[toml]>=1.7.5,<2.0.0",
    "safety>=2.3.0,<3.0.0",
    "pip-audit>=2.6.0,<3.0.0",
    "semgrep>=1.45.0,<2.0.0",
]

# Monitoring and observability
monitoring = [
    "prometheus-client>=0.19.0,<1.0.0",
    "opentelemetry-api>=1.21.0,<2.0.0",
    "opentelemetry-sdk>=1.21.0,<2.0.0",
    "opentelemetry-exporter-jaeger>=1.21.0,<2.0.0",
    "opentelemetry-instrumentation-fastapi>=0.42b0,<1.0.0",
    "opentelemetry-instrumentation-sqlalchemy>=0.42b0,<1.0.0",
    "sentry-sdk[fastapi]>=1.38.0,<2.0.0",
]

# Database extras for different backends
postgresql = [
    "asyncpg>=0.29.0,<1.0.0",
    "psycopg2-binary>=2.9.0,<3.0.0",
]

mysql = [
    "aiomysql>=0.2.0,<1.0.0",
    "pymysql>=1.1.0,<2.0.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/enterprise-user-login-system"
Documentation = "https://yourusername.github.io/enterprise-user-login-system"
Repository = "https://github.com/yourusername/enterprise-user-login-system"
"Bug Tracker" = "https://github.com/yourusername/enterprise-user-login-system/issues"
Changelog = "https://github.com/yourusername/enterprise-user-login-system/blob/main/CHANGELOG.md"

[project.scripts]
enterprise-login = "src.main:main"

# =============================================================================
# TOOL CONFIGURATIONS
# =============================================================================

[tool.ruff]
# Modern Python linting with security and performance rules
line-length = 88
target-version = "py311"
src = ["src", "tests"]

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # Pyflakes
    "UP",  # pyupgrade
    "B",   # flake8-bugbear
    "SIM", # flake8-simplify
    "I",   # isort
    "S",   # bandit (security)
    "DTZ", # flake8-datetimez
    "LOG", # flake8-logging
    "G",   # flake8-logging-format
    "PIE", # flake8-pie
    "PT",  # flake8-pytest-style
    "RUF", # Ruff-specific rules
    "ASYNC", # flake8-async
    "TRIO", # trio specific rules
    "ARG", # flake8-unused-arguments
    "PTH", # flake8-use-pathlib
    "ERA", # eradicate (commented code)
    "PL",  # pylint
    "TRY", # tryceratops (exception handling)
    "FLY", # flynt (f-string conversion)
    "PERF", # perflint (performance)
]

ignore = [
    "E501",   # Line too long (handled by black)
    "S101",   # Use of assert (needed for tests)
    "S104",   # Possible binding to all interfaces
    "PLR0913", # Too many arguments to function call
    "TRY003", # Avoid specifying long messages outside the exception class
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = [
    "S101",    # Assert statements are OK in tests
    "S106",    # Hardcoded passwords are OK in tests
    "ARG001",  # Unused function arguments (fixtures)
    "PLR2004", # Magic value used in comparison
]
"src/main.py" = [
    "S104",    # Binding to all interfaces is needed for server
]

[tool.ruff.lint.isort]
known-first-party = ["src"]
force-single-line = false

[tool.mypy]
# Strict type checking for enterprise code quality
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_any_generics = true
disallow_subclassing_any = true
disallow_untyped_calls = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
namespace_packages = true

# Type checking for third-party libraries
plugins = [
    "pydantic.mypy",
    "sqlalchemy.ext.mypy.plugin"
]

# Per-module configurations
[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disable_error_code = ["attr-defined", "arg-type"]

[[tool.mypy.overrides]]
module = [
    "uvicorn.*",
    "prometheus_client.*",
    "structlog.*",
    "passlib.*",
    "jose.*",
    "aiosmtplib.*",
    "psutil.*",
    "hypothesis.*",
    "locust.*",
]
ignore_missing_imports = true

[tool.black]
line-length = 88
target-version = ["py311"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # Exclude generated files
  migrations/
  | __pycache__/
  | \.git/
  | \.venv/
  | dist/
  | build/
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["src"]
known_third_party = [
    "fastapi",
    "pydantic",
    "sqlalchemy",
    "alembic",
    "pytest",
    "httpx",
    "structlog",
    "uvicorn",
]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "-q",
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=90",
    "--timeout=30",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
norecursedirs = [".venv", "dist", "build", "legacy_tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "security: marks tests as security tests", 
    "performance: marks tests as performance tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
]
asyncio_mode = "auto"
timeout = 30
timeout_method = "thread"

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
    "setup.py",
]
branch = true
parallel = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.bandit]
exclude_dirs = ["tests", "migrations"]
tests = ["B201", "B301", "B302", "B303", "B304", "B305", "B306", "B307", "B308", "B309", "B310", "B311", "B312", "B313", "B314", "B315", "B316", "B317", "B318", "B319", "B320", "B321", "B322", "B323", "B324", "B325", "B601", "B602", "B603", "B604", "B605", "B606", "B607", "B608", "B609", "B610", "B611", "B701", "B702", "B703"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection in specific contexts

[tool.bandit.assert_used]
skips = ["*/tests/*"]

# Security configurations
[tool.pip-audit]
# Audit Python packages for known vulnerabilities
desc = false
require-hashes = false
local = false

[tool.safety]
# Safety checks for known security vulnerabilities
# Configuration for the safety tool
full-report = true
json = false

[tool.pre-commit]

[[tool.pre-commit.repos]]
repo = "https://github.com/pre-commit/pre-commit-hooks"
rev  = "v4.5.0"
hooks = [
  { id = "trailing-whitespace" },
  { id = "end-of-file-fixer" },
  { id = "check-yaml" },
  { id = "check-added-large-files" },
  { id = "check-json" },
  { id = "check-toml" },
  { id = "check-merge-conflict" },
  { id = "debug-statements" },
]

[[tool.pre-commit.repos]]
repo = "https://github.com/psf/black"
rev  = "23.11.0"
hooks = [
  { id = "black" },
]

[[tool.pre-commit.repos]]
repo = "https://github.com/pycqa/isort"
rev  = "5.12.0"
hooks = [
  { id = "isort" },
]

[[tool.pre-commit.repos]]
repo = "https://github.com/astral-sh/ruff-pre-commit"
rev  = "v0.1.6"
hooks = [
  { id = "ruff", args = ["--fix"] },
]

[[tool.pre-commit.repos]]
repo = "https://github.com/pre-commit/mirrors-mypy"
rev  = "v1.7.1"
hooks = [
  { id = "mypy", additional_dependencies = ["pydantic", "types-redis"] },
]

[[tool.pre-commit.repos]]
repo = "https://github.com/PyCQA/bandit"
rev  = "1.7.5"
hooks = [
  { id = "bandit", args = ["-c", "pyproject.toml"], additional_dependencies = ["bandit[toml]"] },
]

# Documentation generation
[tool.mkdocs]
site_name        = "Enterprise User Login System"
site_description = "Enterprise-grade user authentication system with comprehensive security and observability"
site_author      = "Enterprise Development Team"
repo_url         = "https://github.com/yourusername/enterprise-user-login-system"
edit_uri         = "edit/main/docs/"

[tool.mkdocs.theme]
name     = "material"
features = [
  "navigation.tabs",
  "navigation.sections",
  "navigation.expand",
  "navigation.top",
  "search.highlight",
  "search.share",
  "content.code.annotate",
]

# array of tables for each palette entry
[[tool.mkdocs.theme.palette]]
scheme  = "default"
primary = "indigo"
accent  = "indigo"
toggle = { icon = "material/brightness-7", name = "Switch to dark mode" }

[[tool.mkdocs.theme.palette]]
scheme  = "slate"
primary = "indigo"
accent  = "indigo"
toggle = { icon = "material/brightness-4", name = "Switch to light mode" }


# Performance and optimization
[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
"*" = ["*.txt", "*.md", "*.yml", "*.yaml", "*.json"]