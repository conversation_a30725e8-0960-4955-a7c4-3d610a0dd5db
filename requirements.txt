# Core Dependencies
fastapi>=0.104.0,<1.0.0
uvicorn[standard]>=0.24.0,<1.0.0
pydantic[email]>=2.5.0,<3.0.0
pydantic-settings>=2.0.0,<3.0.0
sqlalchemy[asyncio]>=2.0.0,<3.0.0
alembic>=1.13.0,<2.0.0
aiosqlite>=0.19.0,<1.0.0
asyncpg>=0.29.0,<1.0.0
passlib[bcrypt]>=1.7.4,<2.0.0
python-jose[cryptography]>=3.3.0,<4.0.0
python-multipart>=0.0.6,<1.0.0
cryptography>=41.0.0,<42.0.0
httpx>=0.25.0,<1.0.0
authlib>=1.2.0,<2.0.0
aiosmtplib>=3.0.0,<4.0.0
email-validator>=2.1.0,<3.0.0
python-dotenv>=1.0.0,<2.0.0
structlog>=23.2.0,<24.0.0
prometheus-client>=0.19.0,<1.0.0
redis>=5.0.0,<6.0.0
psutil>=5.9.0,<6.0.0
sentry-sdk[fastapi]>=1.38.0,<2.0.0

# Development Dependencies
pytest>=7.4.0,<8.0.0
pytest-asyncio>=0.21.0,<1.0.0
pytest-cov>=4.1.0,<5.0.0
pytest-mock>=3.12.0,<4.0.0
pytest-timeout>=2.2.0,<3.0.0
pytest-xdist>=3.5.0,<4.0.0
hypothesis>=6.88.0,<7.0.0
testcontainers>=3.7.0,<4.0.0
locust>=2.17.0,<3.0.0
mypy>=1.7.0,<2.0.0
ruff>=0.1.0,<1.0.0
black>=23.10.0,<24.0.0
isort>=5.12.0,<6.0.0
pre-commit>=3.5.0,<4.0.0
bandit[toml]>=1.7.5,<2.0.0
safety>=2.3.0,<3.0.0
pip-audit>=2.6.0,<3.0.0
semgrep>=1.45.0,<2.0.0
mkdocs>=1.5.0,<2.0.0
mkdocs-material>=9.4.0,<10.0.0
mkdocstrings[python]>=0.24.0,<1.0.0

# Production Dependencies
# Note: These are already included in core dependencies or are specific to production environments (e.g., asyncpg for PostgreSQL)

# Security-focused dependencies
# Note: These are already included in development dependencies

# Monitoring and observability
opentelemetry-api>=1.21.0,<2.0.0
opentelemetry-sdk>=1.21.0,<2.0.0
opentelemetry-exporter-jaeger>=1.21.0,<2.0.0
opentelemetry-instrumentation-fastapi>=0.42b0,<1.0.0
opentelemetry-instrumentation-sqlalchemy>=0.42b0,<1.0.0

# Database extras
psycopg2-binary>=2.9.0,<3.0.0
aiomysql>=0.2.0,<1.0.0
pymysql>=1.1.0,<2.0.0