# .env.example

# Example environment configuration for the user login system

# Database configuration
DATABASE_URL=postgresql://username:password@localhost:5432/mydatabase

# Redis configuration (if applicable)
REDIS_URL=redis://localhost:6379/0

# Security settings
SECRET_KEY=your_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION=3600

# Email service configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# Logging configuration
LOG_LEVEL=INFO

# Other configurations
DEBUG=True

# --- Enhanced Enterprise Configuration ---
ENVIRONMENT=development
DEBUG=true

# Security Configuration
SECURITY_JWT_ALGORITHM=RS256
SECURITY_PASSWORD_MIN_LENGTH=12
SECURITY_ENABLE_RATE_LIMITING=true

# Database Configuration (Enhanced)
DATABASE_TYPE=sqlite
DATABASE_POOL_SIZE=20
DATABASE_POOL_TIMEOUT=30

# Email Configuration (Enhanced)
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USERNAME=<EMAIL>
EMAIL_SMTP_PASSWORD=your-app-password
EMAIL_FROM_EMAIL=<EMAIL>
EMAIL_FROM_NAME=Enterprise Login System

# Redis Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0

# Observability Configuration
OBSERVABILITY_LOG_LEVEL=INFO
OBSERVABILITY_ENABLE_METRICS=true
OBSERVABILITY_ENABLE_TRACING=false
OBSERVABILITY_PROMETHEUS_PORT=9090
OBSERVABILITY_SENTRY_DSN=

# OAuth Configuration (Optional)
OAUTH_GOOGLE_CLIENT_ID=your-google-client-id
OAUTH_GOOGLE_CLIENT_SECRET=your-google-client-secret