# Product Requirements Document (PRD): User Login System

<!--
ROBUST COMPLETION ANNOTATION

- All core features (registration, login, password reset, Google OAuth, /me endpoint) are implemented and type-safe.
- Security: No hardcoded secrets, all sensitive config via env vars. Passwords hashed, JWTs used, input validated.
- Observability: Logging and error handling per Gold Standard.
- DDD structure and async code throughout.

CRITICAL AREAS NEEDING ATTENTION

- Test Infrastructure: AsyncSession fixture issues caused repeated test failures. Must ensure all tests receive a real AsyncSession, not an async generator.
- Test Isolation: Data collisions (duplicate emails) caused IntegrityErrors. Each test must use unique data or truncate tables before each run.
- Coverage: 90%+ coverage not yet achieved; some business logic and error paths lack tests.
- Email/OAuth: Email sending and Google OAuth flows are not fully covered by tests and may fail in CI without proper mocking or secrets.

AREAS OF SIGNIFICANT DOOM

- If test infra is broken (e.g., db_session yields wrong type), all other quality controls are moot—must fix first.
- If secrets/config are ever hardcoded, this is a critical security risk.
- If error handling is not comprehensive, failures may be silent or confusing in production.
- If coverage gates are enforced before test infra is solid, progress will be blocked by non-business-logic failures.
-->

## Overview
A secure, production-grade user login system for a small business website, supporting email/password and Google OAuth login, password reset via Gmail, and robust security, maintainability, and observability.

---

## Functional Requirements
- User registration (email/password, self-service)
- User login (email/password)
- Google OAuth login
- Password reset (request and confirm via email)
- Authenticated user info endpoint
- All endpoints async, type-safe, and robustly validated

---

## Non-Functional Requirements
- SQLite database (cloud-ready)
- No hardcoded secrets; all credentials via environment variables
- Logging and error handling per Gold Standard Guidelines
- Code coverage, static analysis, and security scanning

---

## Implementation Steps

| Step | Description | Status |
|------|-------------|--------|
| 1 | Workspace and DDD structure setup | ✅ Completed |
| 2 | Requirements clarification and threat model | ✅ Completed |
| 3 | pyproject.toml with pinned, justified dependencies | ✅ Completed |
| 4 | Secure password hashing and JWT utilities | ✅ Completed |
| 5 | Async SQLite DB integration | ✅ Completed |
| 6 | Async Gmail email service | ✅ Completed |
| 7 | SQLAlchemy ORM user model | ✅ Completed |
| 8 | Pydantic schemas for API | ✅ Completed |
| 9 | User registration/login/password reset endpoints | ✅ Completed |
| 10 | Authenticated user endpoint (/me) | ✅ Completed |
| 11 | Google OAuth login/callback endpoints | ✅ Completed |
| 12 | get_current_user dependency for authentication | ✅ Completed |
| 13 | Error handling, logging, and type safety | ✅ Completed |
| 14 | Linting, static analysis, and test scaffolding | ✅ Completed |
| 15 | Comprehensive test suite (unit, integration, edge) | ⬜ Pending |
| 16 | Documentation and deployment guide | ⬜ Pending |

---

## Next Steps
- [ ] Add/verify test coverage and static analysis
- [ ] Add OpenAPI and usage documentation
- [ ] Prepare for deployment (env, health checks, etc.)

---

## Notes
- All code follows Gold Standard Guidelines and Critical Coding Rules
- All third-party dependencies are justified and pinned
- No hardcoded secrets; all sensitive config via environment variables
- All endpoints are async and type-safe

---

## CTO Report: Next Steps

**1. Test Infrastructure**
   - Audit all test fixtures. Ensure every test receives a real `AsyncSession` (not an async generator).
   - Add/verify a fixture that truncates user tables before each test for true isolation.

**2. Test Coverage**
   - Write/expand tests for all business logic, error paths, and edge cases.
   - Mock external services (email, OAuth) in tests to avoid CI failures.

**3. Security & Configuration**
   - Double-check for any hardcoded secrets or credentials.
   - Ensure `.env` and secret management are enforced in all environments.

**4. Error Handling & Observability**
   - Review all endpoints and services for comprehensive, actionable error handling.
   - Ensure structured logging and traceability for all critical flows.

**5. Documentation & Dev Experience**
   - Keep README, PRD, and OpenAPI docs up to date.
   - Document known limitations and TODOs in code and docs.

**6. Quality Gates**
   - Only enforce 90%+ coverage and static analysis gates after test infra is proven solid.
   - Use CI to run all quality checks on every PR.

**7. Iterative Improvement**
   - Use the Interactive-Refine-Loop: review, feedback, retest, and document after every major change.

---

**Summary:**
You have a strong foundation and clear standards. The main blockers are test infrastructure and isolation. Fix those first, then expand coverage and error handling, and you’ll be able to enforce all your Gold Standard and CCR requirements with confidence.
