import os
from typing import Any, Optional
import uuid

import pytest
# No direct AsyncClient import here; using async_client fixture
from main import app

# Test secrets from environment variables
test_email = os.getenv("TEST_EMAIL", "<EMAIL>")
test_password = os.getenv("TEST_PASSWORD", "StrongPassw0rd!")

# Assertion helpers
def expect_equal(a: Any, b: Any, msg: Optional[str] = None) -> None:
    if a != b:
        raise AssertionError(msg or f"Expected {a} == {b}")

def expect_true(expr: Any, msg: Optional[str] = None) -> None:
    if not expr:
        raise AssertionError(msg or "Expected expression to be True")

# Generate unique emails
def unique_email() -> str:
    return f"test_{uuid.uuid4().hex[:8]}@example.com"

@pytest.mark.asyncio
async def test_register_and_login_user(async_client):
    email = unique_email()
    # Register user
    resp = await async_client.post(
        "/register",
        json={"email": email, "password": test_password},
    )
    expect_equal(resp.status_code, 201)
    data = resp.json()
    expect_equal(data["email"], email)

    # Login user
    resp = await async_client.post(
        "/login",
        json={"email": email, "password": test_password},
    )
    expect_equal(resp.status_code, 200)
    token = resp.json()["access_token"]
    expect_true(token)

@pytest.mark.asyncio
async def test_register_duplicate_email(async_client):
    email = unique_email()
    await async_client.post(
        "/register",
        json={"email": email, "password": test_password},
    )
    resp = await async_client.post(
        "/register",
        json={"email": email, "password": test_password},
    )
    expect_equal(resp.status_code, 400)

@pytest.mark.asyncio
async def test_me_endpoint_requires_auth(async_client):
    resp = await async_client.get("/me")
    expect_equal(resp.status_code, 401)

@pytest.mark.asyncio
async def test_me_endpoint_authenticated(async_client):
    email = unique_email()
    await async_client.post("/register", json={"email": email, "password": test_password})
    login_resp = await async_client.post(
        "/login", json={"email": email, "password": test_password}
    )
    token = login_resp.json()["access_token"]

    # Authenticated request
    resp = await async_client.get(
        "/me", headers={"Authorization": f"Bearer {token}"}
    )
    expect_equal(resp.status_code, 200)
    data = resp.json()
    expect_equal(data["email"], email)

@pytest.mark.asyncio
async def test_me_endpoint_invalid_token(async_client):
    resp = await async_client.get(
        "/me", headers={"Authorization": "Bearer invalidtoken"}
    )
    assert resp.status_code in (401, 403)

@pytest.mark.asyncio
async def test_register_invalid_email(async_client):
    resp = await async_client.post(
        "/register",
        json={"email": "not-an-email", "password": test_password},
    )
    assert resp.status_code == 422

@pytest.mark.asyncio
async def test_login_wrong_password(async_client):
    email = unique_email()
    await async_client.post(
        "/register", json={"email": email, "password": test_password}
    )
    resp = await async_client.post(
        "/login", json={"email": email, "password": "wrongpassword"}
    )
    assert resp.status_code == 401
    data = resp.json()
    assert "detail" in data

# End of tests.

