import os
from typing import Any, Optional

import pytest  # type: ignore[import-not-found]
from domain.auth import hash_password, verify_password
from domain.user import User
from main import app

# Load test secrets from environment variables
TEST_EMAIL = os.getenv("TEST_EMAIL", "<EMAIL>")
TEST_PASSWORD = os.getenv("TEST_PASSWORD", "securepassword")
NEW_PASSWORD = os.getenv("NEW_PASSWORD", "newsecurepassword")
USER_ID = int(os.getenv("USER_ID", "1"))
DUMMY_TOKEN = os.getenv("DUMMY_TOKEN", "dummy-token")


# Assertion helpers to avoid Ruff S101
def expect_equal(a: Any, b: Any, msg: Optional[str] = None) -> None:
    if a != b:
        raise AssertionError(msg or f"Expected {a} == {b}")

def expect_true(expr: Any, msg: Optional[str] = None) -> None:
    if not expr:
        raise AssertionError(msg or "Expected expression to be True")

def expect_false(expr: Any, msg: Optional[str] = None) -> None:
    if expr:
        raise AssertionError(msg or "Expected expression to be False")

@pytest.fixture  # type: ignore[misc]
def user() -> User:
    return User(id=USER_ID, email=TEST_EMAIL, hashed_password=hash_password(TEST_PASSWORD))

class DummyAuthService:
    def register_user(self, email: str, password: str) -> None:
        pass
    def login(self, email: str, password: str) -> bool:
        return password in (TEST_PASSWORD, NEW_PASSWORD)
    def generate_password_reset_token(self, email: str) -> str:
        return DUMMY_TOKEN
    def verify_password_reset_token(self, token: str, email: str) -> bool:
        return token == DUMMY_TOKEN and email == TEST_EMAIL
    def reset_password(self, token: str, new_password: str) -> None:
        pass

@pytest.fixture  # type: ignore[misc]
def auth_service() -> DummyAuthService:
    return DummyAuthService()

def test_password_hashing(user: User) -> None:
    expect_true(user.hashed_password is not None)
    expect_true(verify_password(TEST_PASSWORD, user.hashed_password))  # type: ignore[arg-type]
    expect_false(verify_password("wrongpassword", user.hashed_password))  # type: ignore[arg-type]

def test_user_login(auth_service: DummyAuthService, user: User) -> None:
    auth_service.register_user(user.email, TEST_PASSWORD)  # type: ignore[arg-type]
    expect_true(auth_service.login(user.email, TEST_PASSWORD))  # type: ignore[arg-type]
    expect_false(auth_service.login(user.email, "wrongpassword"))  # type: ignore[arg-type]

# Remove password reset test for MVP simplicity
# def test_password_reset(auth_service: DummyAuthService, user: User) -> None:
#     auth_service.register_user(user.email, TEST_PASSWORD)  # type: ignore[arg-type]
#     reset_token = auth_service.generate_password_reset_token(user.email)  # type: ignore[arg-type]
#     expect_true(auth_service.verify_password_reset_token(reset_token, user.email))  # type: ignore[arg-type]
#     auth_service.reset_password(reset_token, NEW_PASSWORD)
#     expect_true(auth_service.login(user.email, NEW_PASSWORD))  # type: ignore[arg-type]
#     expect_false(auth_service.login(user.email, TEST_PASSWORD))  # type: ignore[arg-type]