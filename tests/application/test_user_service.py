# FILE: tests/application/test_user_service.py

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from src.application.user_service import UserService
from src.shared.errors.exceptions import BusinessRuleViolation

@pytest.mark.asyncio
async def test_register_user_success(db_session: AsyncSession):
    """Tests successful user registration in the service layer."""
    user_service = UserService()
    email = "<EMAIL>"
    password = "Password123!"
    
    user = await user_service.register_user(db_session, email, password)
    
    assert user is not None
    assert user.email == email
    assert user.id is not None
    
    # Verify it's actually in the DB
    retrieved_user = await user_service.get_user_by_email(db_session, email)
    assert retrieved_user is not None
    assert retrieved_user.id == user.id

@pytest.mark.asyncio
async def test_register_user_duplicate_email_fails(db_session: AsyncSession):
    """Tests that registering a user with a duplicate email raises BusinessRuleViolation."""
    user_service = UserService()
    email = "<EMAIL>"
    password = "Password123!"
    
    # Create the first user
    await user_service.register_user(db_session, email, password)
    
    # Attempt to create a second user with the same email
    with pytest.raises(BusinessRuleViolation) as excinfo:
        await user_service.register_user(db_session, email, "another_password")
    
    assert "EMAIL_ALREADY_EXISTS" in str(excinfo.value)