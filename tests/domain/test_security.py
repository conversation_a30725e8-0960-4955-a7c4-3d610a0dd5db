# FILE: tests/domain/test_security.py

import pytest
from datetime import timedelta
from src.shared.security import (
    hash_password,
    verify_password,
    create_access_token,
    verify_access_token,
)

def test_password_hashing():
    """
    Tests that password hashing and verification work correctly.
    """
    password = "a_very_Secure_p4ssword!"
    hashed_pw = hash_password(password)
    
    # Hash should not be the same as the password
    assert password != hashed_pw
    # Verification should succeed with the correct password
    assert verify_password(password, hashed_pw) is True
    # Verification should fail with an incorrect password
    assert verify_password("wrong_password", hashed_pw) is False

def test_jwt_token_creation_and_verification():
    """
    Tests that JWT tokens are created and can be successfully decoded.
    """
    user_id = 123
    token_data = {"sub": str(user_id)}
    
    token = create_access_token(token_data, expires_delta=timedelta(minutes=15))
    assert isinstance(token, str)
    
    payload = verify_access_token(token)
    assert payload["sub"] == str(user_id)

def test_jwt_token_expiration():
    """
    Tests that an expired JWT token raises a validation error.
    """
    user_id = 456
    # Create a token that expired 1 second ago
    expired_token = create_access_token(
        {"sub": str(user_id)}, expires_delta=timedelta(seconds=-1)
    )
    
    with pytest.raises(ValueError, match="Invalid or expired token"):
        verify_access_token(expired_token)