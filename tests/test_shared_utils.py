"""Test suite for shared utilities module."""

import pytest
import uuid
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from sqlalchemy.ext.asyncio import AsyncSession

from src.shared.utils import (
    get_current_user, generate_unique_id, format_email, is_valid_password
)
from src.shared.errors.exceptions import SecurityError


class TestUtilityFunctions:
    """Test cases for utility functions."""

    def test_generate_unique_id(self):
        """Test unique ID generation."""
        id1 = generate_unique_id()
        id2 = generate_unique_id()
        
        assert isinstance(id1, str)
        assert isinstance(id2, str)
        assert id1 != id2
        assert len(id1) == 36  # UUID4 length
        assert len(id2) == 36

    def test_format_email_valid(self):
        """Test email formatting with valid emails."""
        # Test normal email
        result = format_email("<EMAIL>")
        assert result == "<EMAIL>"
        
        # Test email with spaces
        result = format_email("  <EMAIL>  ")
        assert result == "<EMAIL>"
        
        # Test already lowercase
        result = format_email("<EMAIL>")
        assert result == "<EMAIL>"

    def test_format_email_none(self):
        """Test email formatting with None input."""
        result = format_email(None)
        assert result == ""

    def test_format_email_empty(self):
        """Test email formatting with empty string."""
        result = format_email("")
        assert result == ""
        
        result = format_email("   ")
        assert result == ""

    def test_is_valid_password_strong(self):
        """Test password validation with strong passwords."""
        strong_passwords = [
            "StrongPassword123!",
            "MySecure@Pass1",
            "Complex#Password9",
            "Secure$123Pass"
        ]
        
        for password in strong_passwords:
            assert is_valid_password(password) is True

    def test_is_valid_password_weak(self):
        """Test password validation with weak passwords."""
        weak_passwords = [
            "weak",
            "password",
            "123456",
            "Password",  # No numbers or special chars
            "password123",  # No uppercase or special chars
            "PASSWORD123",  # No lowercase or special chars
            "Pass123",  # Too short
            "",  # Empty
            "   "  # Whitespace only
        ]
        
        for password in weak_passwords:
            assert is_valid_password(password) is False


class TestGetCurrentUser:
    """Test cases for get_current_user dependency."""

    @pytest.mark.asyncio
    async def test_get_current_user_success(self, db_session: AsyncSession):
        """Test successful user retrieval from valid token."""
        # First register a user
        from src.application.user_service import UserService
        from src.shared.security import create_access_token
        
        user_service = UserService()
        email = f"testuser_{uuid.uuid4().hex[:8]}@example.com"
        password = "StrongPassword123!"
        
        user = await user_service.register_user(db_session, email, password)
        
        # Create a valid token
        token = create_access_token({"sub": str(user.id)})
        
        # Test get_current_user
        with patch('src.shared.utils.Depends') as mock_depends:
            # Mock the dependencies
            mock_depends.side_effect = lambda x: x
            
            result = await get_current_user(token, db_session)
            
            assert result is not None
            assert result.id == user.id
            assert result.email == email

    @pytest.mark.asyncio
    async def test_get_current_user_invalid_token(self, db_session: AsyncSession):
        """Test get_current_user with invalid token."""
        with patch('src.shared.utils.Depends') as mock_depends:
            mock_depends.side_effect = lambda x: x
            
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user("invalid_token", db_session)
            
            assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_get_current_user_expired_token(self, db_session: AsyncSession):
        """Test get_current_user with expired token."""
        from src.shared.security import create_access_token
        from datetime import timedelta
        
        # Create expired token
        expired_token = create_access_token(
            {"sub": "123"}, expires_delta=timedelta(seconds=-1)
        )
        
        with patch('src.shared.utils.Depends') as mock_depends:
            mock_depends.side_effect = lambda x: x
            
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user(expired_token, db_session)
            
            assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_get_current_user_user_not_found(self, db_session: AsyncSession):
        """Test get_current_user when user doesn't exist in database."""
        from src.shared.security import create_access_token
        
        # Create token for non-existent user
        token = create_access_token({"sub": "99999"})
        
        with patch('src.shared.utils.Depends') as mock_depends:
            mock_depends.side_effect = lambda x: x
            
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user(token, db_session)
            
            assert exc_info.value.status_code == 404

    @pytest.mark.asyncio
    async def test_get_current_user_malformed_token(self, db_session: AsyncSession):
        """Test get_current_user with malformed token payload."""
        from src.shared.security import create_access_token
        
        # Create token without 'sub' field
        token = create_access_token({"user": "123"})
        
        with patch('src.shared.utils.Depends') as mock_depends:
            mock_depends.side_effect = lambda x: x
            
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user(token, db_session)
            
            assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_get_current_user_invalid_user_id(self, db_session: AsyncSession):
        """Test get_current_user with non-numeric user ID in token."""
        from src.shared.security import create_access_token
        
        # Create token with non-numeric user ID
        token = create_access_token({"sub": "not_a_number"})
        
        with patch('src.shared.utils.Depends') as mock_depends:
            mock_depends.side_effect = lambda x: x
            
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user(token, db_session)
            
            assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_get_current_user_database_error(self, db_session: AsyncSession):
        """Test get_current_user when database error occurs."""
        from src.shared.security import create_access_token
        
        token = create_access_token({"sub": "123"})
        
        with patch('src.shared.utils.Depends') as mock_depends:
            mock_depends.side_effect = lambda x: x
            
            with patch('src.application.user_service.UserService.get_user_by_id') as mock_get_user:
                mock_get_user.side_effect = Exception("Database error")
                
                with pytest.raises(Exception):  # Should propagate database errors
                    await get_current_user(token, db_session)


class TestPasswordValidation:
    """Additional test cases for password validation edge cases."""

    def test_password_validation_edge_cases(self):
        """Test password validation with edge cases."""
        # Test minimum length boundary
        assert is_valid_password("Pass1!") is False  # 6 chars, too short
        assert is_valid_password("Pass12!") is False  # 7 chars, still too short
        assert is_valid_password("Pass123!") is True  # 8 chars, minimum
        
        # Test character requirements
        assert is_valid_password("password123!") is False  # No uppercase
        assert is_valid_password("PASSWORD123!") is False  # No lowercase
        assert is_valid_password("Password!") is False  # No numbers
        assert is_valid_password("Password123") is False  # No special chars
        
        # Test with various special characters
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        for char in special_chars:
            password = f"Password123{char}"
            assert is_valid_password(password) is True

    def test_password_validation_unicode(self):
        """Test password validation with unicode characters."""
        # Test with unicode characters
        unicode_passwords = [
            "Pássword123!",  # Accented characters
            "密码Password123!",  # Chinese characters
            "Пароль123!",  # Cyrillic characters
        ]
        
        for password in unicode_passwords:
            # Behavior may vary based on implementation
            result = is_valid_password(password)
            assert isinstance(result, bool)


class TestEmailFormatting:
    """Additional test cases for email formatting."""

    def test_format_email_special_cases(self):
        """Test email formatting with special cases."""
        # Test with tabs and newlines
        result = format_email("\<EMAIL>\n")
        assert result == "<EMAIL>"
        
        # Test with multiple spaces
        result = format_email("   <EMAIL>   ")
        assert result == "<EMAIL>"
        
        # Test with mixed case
        result = format_email("<EMAIL>")
        assert result == "<EMAIL>"

    def test_format_email_edge_cases(self):
        """Test email formatting edge cases."""
        # Test with only whitespace
        assert format_email("\t\n  ") == ""
        
        # Test with zero-width characters (if applicable)
        assert format_email("\u200b") == ""
        
        # Test with very long email
        long_email = "a" * 100 + "@" + "b" * 100 + ".com"
        result = format_email(long_email.upper())
        assert result == long_email.lower()


class TestUniqueIdGeneration:
    """Additional test cases for unique ID generation."""

    def test_generate_unique_id_format(self):
        """Test that generated IDs follow UUID4 format."""
        import re
        
        uuid_pattern = re.compile(
            r'^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$',
            re.IGNORECASE
        )
        
        for _ in range(10):
            unique_id = generate_unique_id()
            assert uuid_pattern.match(unique_id)

    def test_generate_unique_id_collision_resistance(self):
        """Test that generated IDs are sufficiently unique."""
        # Generate many IDs and check for collisions
        ids = set()
        num_ids = 1000
        
        for _ in range(num_ids):
            new_id = generate_unique_id()
            assert new_id not in ids  # No collisions
            ids.add(new_id)
        
        assert len(ids) == num_ids

    def test_generate_unique_id_thread_safety(self):
        """Test unique ID generation in concurrent scenarios."""
        import asyncio
        import threading
        from concurrent.futures import ThreadPoolExecutor
        
        def generate_ids(count):
            return [generate_unique_id() for _ in range(count)]
        
        # Test with multiple threads
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(generate_ids, 100) for _ in range(4)]
            all_ids = []
            
            for future in futures:
                all_ids.extend(future.result())
        
        # Check that all IDs are unique
        assert len(all_ids) == len(set(all_ids))